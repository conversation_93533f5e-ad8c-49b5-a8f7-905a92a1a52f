# PRD.txt - Job Notification Module for Task Master MCP Server

## Product Overview

This PRD outlines the implementation of a **Job Notification Module** that will be integrated into the existing Task Master MCP Server to provide real-time job posting notifications to external services via webhook calls.

## Feature Description

### **Job On New Job Notification Module**

A new module that automatically triggers webhook notifications when a new job is created through the JobsController API endpoint.

## Technical Requirements

### **Integration Point**
- **Location**: JobsController POST API endpoint
- **Trigger**: After successful execution of `JobsService.create()`
- **Type**: Asynchronous webhook notification

### **Module Architecture**

#### **1. Webhook Service Component**
- **Service Name**: `JobNotificationService`
- **Method**: `notifyJobPosted(jobData)`
- **HTTP Method**: POST
- **Endpoint**: Configurable via environment variable
- **Default URL**: `http://localhost:8000/api/webhooks/job-posted`

#### **2. Environment Configuration**
```
WEBHOOK_NOTIFICATION_URL=http://localhost:8000/api/webhooks/job-posted
WEBHOOK_TOKEN=your_webhook_token_here
```

### **Payload Structure**

The webhook payload must include the following standardized structure:

```json
{
    "event_type": "job_posted",
    "event_id": "evt_job_202501150945123456",
    "timestamp": "2025-01-15 09:45:30",
    "job_data": {
        "job_id": "job_abc123def456",
        "title": "Kitchen Sink Plumbing Repair",
        "description": "Need urgent repair for a leaking kitchen sink...",
        "category_id": 15,
        "zip_code": "35210",
        "address": "123 Main Street, New York, NY 10001",
        "latitude": 33.538842,
        "longitude": -86.646299,
        "budget_min": 100.0,
        "budget_max": 300.0,
        "currency": "USD",
        "customer_email": "<EMAIL>",
        "customer_name": "John Doe",
        "created_at": "2025-01-15 09:45:30"
    }
}
```

### **HTTP Headers**
- **Required Header**: `X-Webhook-Token`
- **Value Source**: Environment variable `WEBHOOK_TOKEN`
- **Content-Type**: `application/json`

## Implementation Details

### **Data Collection Requirements**

All payload data must be collected comprehensively from the job creation flow:

#### **Event Metadata**
- `event_type`: Static value "job_posted"
- `event_id`: Generated unique identifier (format: `evt_job_YYYYMMDDHHMMSSNNNN`)
- `timestamp`: Current timestamp in format "YYYY-MM-DD HH:MM:SS"

#### **Job Data Fields**
- `job_id`: Unique job identifier from database
- `title`: Job title from request
- `description`: Job description from request
- `category_id`: Job category identifier
- `zip_code`: Location zip code
- `address`: Full address string
- `latitude`: Geographic latitude coordinate
- `longitude`: Geographic longitude coordinate
- `budget_min`: Minimum budget amount
- `budget_max`: Maximum budget amount
- `currency`: Currency code (default: "USD")
- `customer_email`: Customer email address
- `customer_name`: Customer full name
- `created_at`: Job creation timestamp

### **Error Handling**

#### **Retry Logic**
- **Max Retries**: 3 attempts
- **Backoff Strategy**: Exponential backoff (1s, 2s, 4s)
- **Timeout**: 30 seconds per request

#### **Failure Scenarios**
- Log webhook failures without blocking job creation
- Store failed webhook attempts for manual retry
- Continue job creation process even if webhook fails

### **Security Considerations**

- **Authentication**: X-Webhook-Token header validation
- **HTTPS**: Support for secure webhook endpoints
- **Token Management**: Secure storage of webhook tokens in environment variables

## Acceptance Criteria

### **Functional Requirements**
- [ ] Module successfully triggers after `JobsService.create()` completion
- [ ] All required payload fields are populated from job creation data
- [ ] Webhook call includes proper authentication header
- [ ] Environment variables are properly configured and used
- [ ] Webhook failures do not prevent job creation

### **Non-Functional Requirements**
- [ ] Webhook call completes within 30 seconds
- [ ] Module handles network failures gracefully
- [ ] Logging captures all webhook attempts and results
- [ ] Performance impact on job creation is minimal (<100ms overhead)

## Testing Requirements

### **Unit Tests**
- Payload generation with mock job data
- Environment variable configuration
- Error handling scenarios

### **Integration Tests**
- End-to-end job creation with webhook notification
- Webhook endpoint connectivity
- Authentication header validation

### **Performance Tests**
- Webhook call latency measurement
- Job creation performance impact assessment

## Deployment Considerations

### **Environment Setup**
- Configure `WEBHOOK_NOTIFICATION_URL` for each environment
- Set up secure `WEBHOOK_TOKEN` values
- Verify network connectivity to webhook endpoints

### **Monitoring**
- Track webhook success/failure rates
- Monitor webhook response times
- Alert on consecutive webhook failures

## Future Enhancements

- Support for multiple webhook endpoints
- Configurable retry policies
- Webhook payload customization
- Event filtering capabilities

Sources
