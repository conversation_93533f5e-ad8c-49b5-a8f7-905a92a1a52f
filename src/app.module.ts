import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { ClientsModule } from './clients/clients.module';
import { WebhooksModule } from './webhooks/webhooks.module';
import { StripeModule } from '@golevelup/nestjs-stripe';
import { ConfigModule } from '@nestjs/config';
import * as Joi from '@hapi/joi';
import { RolesModule } from './roles/roles.module';
import { ClientTypeModule } from './client-type/client-type.module';
import { BookingTypesModule } from './booking-types/booking-types.module';
import { MediasModule } from './medias/medias.module';
import { BookingAddonModule } from './booking-addon/booking-addon.module';
import { BookingExtraModule } from './booking-extra/booking-extra.module';
import { BookingRequestModule } from './booking-request/booking-request.module';
import { BookingFrequencyModule } from './booking-frequency/booking-frequency.module';
import { BookingTimeArrivalModule } from './booking-time-arrival/booking-time-arrival.module';
import { SendEmailModule } from './send-email/send-email.module';
import { IssuesModule } from './issues/issues.module';
import { QuotesModule } from './quotes/quotes.module';
import { DiscountModule } from './discount/discount.module';
import { TaxModule } from './tax/tax.module';
import { ActivitiesWorkingModule } from './activities-working/activities-working.module';
import { JobsModule } from './jobs/jobs.module';
import { WorkspaceModule } from './workspace/workspace.module';
import { AccessModule } from './access/access.module';
import { InvoicesModule } from './invoices/invoices.module';
import { WorkspaceMiddleware } from './@core/middleware/workspace/workspace.middleware';
import { GrantsModule } from './grants/grants.module';
import { IamModule } from './iam/iam.module';
import { WorkingHoursModule } from './working-hours/working-hours.module';
import { ClientsDataModule } from './clients-data/clients-data.module';
import { AddressModule } from './address/address.module';
import { TransactionsModule } from './transactions/transactions.module';
import { CommonModule } from './common/common.module';
import { ScheduleModule } from '@nestjs/schedule';
import { CalendarModule } from './calendar/calendar.module';
import { GeneratePdfModule } from './generate-pdf/generate-pdf.module';
import { TokenStorageModule } from './token-storage/token-storage.module';
import { InitialDataModule } from './initial-data/initial-data.module';
import { PackagesModule } from './packages/packages.module';
import { PaymentModule } from './payment/payment.module';
import { SubscriptionModule } from './payment/subcription/subscription.module';
import { BillingModule } from './payment/billing/billing.module';
import { PaymentMethodModule } from './payment/payment_method/payment_method.module';
import { PropertyModule } from './property/property.module';
import { NotificationsModule } from './notifications/notifications.module';
import { InfoModule } from './info/info.module';
import { FrequencyDiscountModule } from './frequency-discount/frequency-discount.module';
import { SmtpConfigModule } from './smtp-config/smtp-config.module';
import { RedisModule } from './redis/redis.module';
import { UserPackagesModule } from './user-packages/user-packages.module';
import { TipOptionsModule } from './tip-options/tip-options.module';
import { EmailTemplatesModule } from "./email-templates/email-templates.module";

@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
            validationSchema: Joi.object({
                DB_STRING: Joi.string().required(),
                SPACES_URL: Joi.string().required(),
                SPACES_BUCKET_NAME: Joi.string().required(),
                SPACES_KEY: Joi.string().required(),
                SPACES_SECRET_KEY: Joi.string().required(),
                SPACES_FOLDER_NAME: Joi.string().required(),
                WEB_APP_URL: Joi.string().required(),
                WEBHOOK_NOTIFICATION_URL: Joi.string().optional(),
                WEBHOOK_TOKEN: Joi.string().optional()
            }),
        }),
        MongooseModule.forRoot(process.env.DB_STRING),
        RedisModule.forRootAsync({
            useFactory: () => {
              return [
                {
                  name: 'COMMON_CACHE_NAME',
                  url: process.env.REDIS_COMMON,
                },
              ];
            },
          }),
        EventEmitterModule.forRoot(),
        ScheduleModule.forRoot(),
        UsersModule,
        SendEmailModule,
        IamModule,
        AddressModule,
        ClientsDataModule,
        ClientsModule,
        ClientTypeModule,
        WebhooksModule,
        AccessModule,
        StripeModule.forRoot(StripeModule, {
            apiKey: process.env.STRIPE_SECRET_KEY,
            webhookConfig: {
                stripeSecrets: {
                    account: process.env.STRIPE_WEBHOOK_SECRET,
                    connect: process.env.STRIPE_WEBHOOK_SECRET
                },
                requestBodyProperty: 'rawBody', // <-- Set to 'rawBody'
            },
        }),
        RolesModule,
        MediasModule,
        BookingTypesModule,
        BookingAddonModule,
        BookingExtraModule,
        BookingRequestModule,
        BookingFrequencyModule,
        BookingTimeArrivalModule,
        IssuesModule,
        QuotesModule,
        DiscountModule,
        TaxModule,
        ActivitiesWorkingModule,
        JobsModule,
        WorkspaceModule,
        InvoicesModule,
        GrantsModule,
        AuthModule,
        WorkingHoursModule,
        TransactionsModule,
        CommonModule,
        CalendarModule,
        GeneratePdfModule,
        TokenStorageModule,
        InitialDataModule,
        PackagesModule,
        PaymentModule,
        SubscriptionModule,
        BillingModule,
        PaymentMethodModule,
        PropertyModule,
        NotificationsModule,
        InfoModule,
        FrequencyDiscountModule,
        SmtpConfigModule,
        UserPackagesModule,
        TipOptionsModule,
        EmailTemplatesModule,
    ],
    controllers: [AppController],
    providers: [AppService],
})
export class AppModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer
            .apply(WorkspaceMiddleware)
            .forRoutes(
                'issues',
                'booking-request',
                'tax',
                'jobs',
                'invoices',
                'booking-time-arrival',
                'booking-types',
                'notification',
            );
    }
}
