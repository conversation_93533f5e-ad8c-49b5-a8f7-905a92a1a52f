import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosResponse } from 'axios';
import { 
  WebhookPayload, 
  WebhookResponse, 
  WebhookConfig,
  WebhookJobData,
  WebhookEventMetadata 
} from './interfaces/webhook-payload.interface';
import { Jobs } from '../jobs/jobs.schema';
import { BookingRequest } from '../booking-request/booking-request.schema';
import { Address } from '../address/address.schema';

@Injectable()
export class JobNotificationService {
  private readonly logger = new Logger(JobNotificationService.name);
  private readonly webhookConfig: WebhookConfig;

  constructor(private readonly configService: ConfigService) {
    this.webhookConfig = {
      url: this.configService.get<string>('WEBHOOK_NOTIFICATION_URL', 'http://localhost:8000/api/webhooks/job-posted'),
      token: this.configService.get<string>('WEBHOOK_TOKEN', ''),
      timeout: 30000, // 30 seconds
      maxRetries: 3,
      retryDelays: [1000, 2000, 4000] // 1s, 2s, 4s exponential backoff
    };
  }

  /**
   * Main method to notify external service about job posting
   */
  async notifyJobPosted(jobData: Jobs, bookingRequest: BookingRequest, address: Address): Promise<WebhookResponse> {
    try {
      const payload = this.buildWebhookPayload(jobData, bookingRequest, address);
      this.logger.log(`Sending webhook notification for job ${jobData._id}`);
      
      const response = await this.sendWebhookWithRetry(payload);
      
      this.logger.log(`Webhook notification sent successfully for job ${jobData._id}`);
      return response;
    } catch (error) {
      this.logger.error(`Failed to send webhook notification for job ${jobData._id}:`, error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Build the webhook payload according to PRD specifications
   */
  private buildWebhookPayload(jobData: Jobs, bookingRequest: BookingRequest, address: Address): WebhookPayload {
    const eventMetadata = this.generateEventMetadata();
    const jobPayloadData = this.buildJobData(jobData, bookingRequest, address);

    return {
      event_type: eventMetadata.event_type,
      event_id: eventMetadata.event_id,
      timestamp: eventMetadata.timestamp,
      job_data: jobPayloadData
    };
  }

  /**
   * Generate event metadata
   */
  private generateEventMetadata(): WebhookEventMetadata {
    const now = new Date();
    const timestamp = now.toISOString().replace('T', ' ').substring(0, 19); // YYYY-MM-DD HH:MM:SS format
    const eventId = `evt_job_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`;

    return {
      event_type: 'job_posted',
      event_id: eventId,
      timestamp
    };
  }

  /**
   * Build job data payload
   */
  private buildJobData(jobData: Jobs, bookingRequest: BookingRequest, address: Address): WebhookJobData {
    // Format address string
    const addressString = this.formatAddress(address);
    
    // Extract coordinates
    const [longitude, latitude] = address.coordinates || [0, 0];

    // Format customer name
    const customerName = `${bookingRequest.firstName} ${bookingRequest.lastName}`.trim();

    // Format created_at timestamp
    const createdAt = jobData.dateCreated.toISOString().replace('T', ' ').substring(0, 19);

    return {
      job_id: jobData._id.toString(),
      title: bookingRequest.title || 'Job Request',
      description: bookingRequest.notes || 'No description provided',
      category_id: 0, // This would need to be mapped from service type
      zip_code: address.zip || '',
      address: addressString,
      latitude: latitude,
      longitude: longitude,
      budget_min: jobData.price || 0,
      budget_max: jobData.price || 0,
      currency: 'USD',
      customer_email: bookingRequest.email,
      customer_name: customerName,
      created_at: createdAt
    };
  }

  /**
   * Format address into a readable string
   */
  private formatAddress(address: Address): string {
    const parts = [
      address.street_one,
      address.street_two,
      address.city,
      address.state,
      address.zip
    ].filter(part => part && part.trim() !== '');

    return parts.join(', ');
  }

  /**
   * Send webhook with retry logic
   */
  private async sendWebhookWithRetry(payload: WebhookPayload, retryCount = 0): Promise<WebhookResponse> {
    try {
      const response = await this.sendWebhook(payload);
      return {
        success: true,
        statusCode: response.status,
        retryCount
      };
    } catch (error) {
      if (retryCount < this.webhookConfig.maxRetries) {
        const delay = this.webhookConfig.retryDelays[retryCount] || 4000;
        this.logger.warn(`Webhook failed, retrying in ${delay}ms (attempt ${retryCount + 1}/${this.webhookConfig.maxRetries})`);
        
        await this.delay(delay);
        return this.sendWebhookWithRetry(payload, retryCount + 1);
      }

      throw error;
    }
  }

  /**
   * Send the actual webhook HTTP request
   */
  private async sendWebhook(payload: WebhookPayload): Promise<AxiosResponse> {
    const headers = {
      'Content-Type': 'application/json',
      'X-Webhook-Token': this.webhookConfig.token
    };

    return axios.post(this.webhookConfig.url, payload, {
      headers,
      timeout: this.webhookConfig.timeout
    });
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
