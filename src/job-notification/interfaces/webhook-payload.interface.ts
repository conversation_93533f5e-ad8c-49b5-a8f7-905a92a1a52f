export interface WebhookEventMetadata {
  event_type: string;
  event_id: string;
  timestamp: string;
}

export interface WebhookJobData {
  job_id: string;
  title: string;
  description: string;
  category_id: number;
  zip_code: string;
  address: string;
  latitude: number;
  longitude: number;
  budget_min: number;
  budget_max: number;
  currency: string;
  customer_email: string;
  customer_name: string;
  created_at: string;
}

export interface WebhookPayload {
  event_type: string;
  event_id: string;
  timestamp: string;
  job_data: WebhookJobData;
}

export interface WebhookResponse {
  success: boolean;
  statusCode?: number;
  error?: string;
  retryCount?: number;
}

export interface WebhookConfig {
  url: string;
  token: string;
  timeout: number;
  maxRetries: number;
  retryDelays: number[];
}
