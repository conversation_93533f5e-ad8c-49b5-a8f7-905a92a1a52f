import { BadRequestException, forwardRef, HttpException, HttpStatus, Inject, Injectable, Logger } from "@nestjs/common";
import { Jobs, JobsDocument } from "./jobs.schema";
import { InjectModel } from "@nestjs/mongoose";
import mongoose, { Model, PipelineStage } from "mongoose";
import { CollectionDto } from "@forlagshuset/nestjs-mongoose-paginate";
import CreateJobDto from "./Dto/CreateJobDto";
import UpdateJobDto from "./Dto/UpdateJobDto";
import { datetime, Options, RRule, rrulestr } from "rrule";
import { ClientsService } from "src/clients/clients.service";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import duration from "dayjs/plugin/duration";
import updateLocale from "dayjs/plugin/updateLocale";
import timezone from "dayjs/plugin/timezone";
import { CounterDto } from "@forlagshuset/nestjs-mongoose-paginate/src/input.dto";
import { paginateWithCount } from "src/utils/helpers";
import { Users, UsersDocument } from "src/users/users.schema";
import { ClientData, ClientsDataDocument } from "src/clients-data/client-data.schema";
import { WorkingHours, WorkingHoursDocument } from "src/working-hours/working-hours.schema";
import { Medias, MediasDocument } from "../medias/medias.schema";
import { NotificationsService } from "../notifications/notifications.service";
import { getNotiMessage } from "../notifications/constant/notification.message";
import { Cron, CronExpression } from "@nestjs/schedule";
import { MobileAppScreens, PortalScreens } from "../notifications/constant/notification.screen";
import { NotificationType } from "../notifications/notifications.schema";
import { UsersService } from "../users/users.service";
import { GeneratePdfService } from "../generate-pdf/generate-pdf.service";
import { Address } from "../address/address.schema";
import { BookingRequest } from "../booking-request/booking-request.schema";
import process from "process";
import { MediasService } from "../medias/medias.service";
import { InvoicesService } from "../invoices/invoices.service";
import { WeekDay } from "../booking-frequency/booking-frequency.types";
import { BookingFrequencyService } from "../booking-frequency/booking-frequency.service";
import { BookingFrequency } from "../booking-frequency/booking-frequency.schema";
import { ReminderEmail } from "./templates/reminder";
import { WorkspaceService } from "src/workspace/workspace.service";
import { SmtpConfigService } from "../smtp-config/smtp-config.service";
import { SendEmailService } from "../send-email/send-email.service";
import { InvoiceSchedulerService } from "src/invoices/invoices-schedule.service";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(updateLocale);
dayjs.extend(duration);
dayjs.updateLocale("en", {
  weekStart: 1
});

export enum BeforeAfterEnum {
  BEFORE = "before",
  AFTER = "after"
}

@Injectable()
export class JobsService {
  private readonly CACHE_DURATION_DAYS = 30 // cache next occurrences for 30 days
  private readonly MAX_OCCURRENCES = 500; // maximum number of occurrences to calculate in advance

  private weekdayMap = {
    [WeekDay.SUNDAY]: RRule.SU,
    [WeekDay.MONDAY]: RRule.MO,
    [WeekDay.TUESDAY]: RRule.TU,
    [WeekDay.WEDNESDAY]: RRule.WE,
    [WeekDay.THURSDAY]: RRule.TH,
    [WeekDay.FRIDAY]: RRule.FR,
    [WeekDay.SATURDAY]: RRule.SA,
  };

  constructor(
    @Inject(forwardRef(() => ClientsService))
    private ClientsService: ClientsService,
    @InjectModel(Jobs.name) private jobsModel: Model<JobsDocument>,
    @InjectModel(Medias.name) private mediasModel: Model<MediasDocument>,
    @InjectModel(Users.name) private usersModel: Model<UsersDocument>,
    @InjectModel(ClientData.name)
    private clientDataModel: Model<ClientsDataDocument>,
    @InjectModel(WorkingHours.name)
    private workingHoursModel: Model<WorkingHoursDocument>,
    private readonly notificationService: NotificationsService,
    private readonly generatePdfService: GeneratePdfService,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
    @Inject(forwardRef(() => MediasService))
    private readonly mediasService: MediasService,
    @Inject(forwardRef(() => InvoicesService))
    private readonly invoicesService: InvoicesService,
    private readonly bookingFrequencyService: BookingFrequencyService,
    @Inject(forwardRef(() => WorkspaceService))
    private WorkspaceService: WorkspaceService,
    @Inject(forwardRef(() => SmtpConfigService))
    private smtpConfigService: SmtpConfigService,
    @Inject(forwardRef(() => SendEmailService))
    private readonly SendEmailService: SendEmailService,
    private readonly invoiceSchedulerService: InvoiceSchedulerService,
  ) {
  }

  private readonly logger = new Logger(JobsService.name);

  @Cron(CronExpression.EVERY_30_SECONDS)
  async cronScanUnassignJobs() {
    const now = dayjs().utc().startOf('day').toDate();
    const threeDaysToNow = dayjs().add(3, "day").toDate();

    try {
      const upcomingThreeDays = await this.jobsModel.find({
        status: {
          $in: ["unassigned"]
        },
        suitableDate: {
          $gte: now,
          $lte: threeDaysToNow
        }
      }).populate("bookingRequest").exec();
      if (upcomingThreeDays.length > 0) {
        for (const job of upcomingThreeDays) {
          const notiMsg = getNotiMessage("");
          const jobUnassignMsg = notiMsg["job-has-not-assigned"](job.bookingRequest.firstName + " " + job.bookingRequest.lastName);
          const owner = await this.usersService.findOwnerByWorkspace(job.workspace._id?.toString());
          if (!owner) return;
          const isSentNoti = await this.notificationService.checkIfAdminNotificationSent(owner._id?.toString(), {
            title: jobUnassignMsg.title,
            body: jobUnassignMsg.body,
            data: {
              screen: PortalScreens.Job,
              id: job._id.toString()
            }
          });
          if (isSentNoti) return;
          await this.notificationService.sendAdminNotification(job.workspace._id?.toString(), {
            title: jobUnassignMsg.title,
            body: jobUnassignMsg.body,
            data: {
              screen: PortalScreens.Job,
              id: job._id?.toString()
            },
          });
        }
      }
    } catch (err) {
      console.log("Error in cronScanUnassignJobs", err);
    }
  }

  @Cron(CronExpression.EVERY_30_SECONDS)
  async cronScanIncomingJobs() {
    const now = dayjs().utc().startOf('day').toDate();
    const threeDaysTillNow = dayjs().add(3, "day").toDate();
    const oneDayTillNow = dayjs().add(1, "day").toDate();
    const oneHourTillNow = dayjs().add(1, "hour").toDate();
    const fifteenMinutesTillNow = dayjs().add(15, "minutes").toDate();

    try {
      const findJobs = async (timeFrame: Date) => {
        return this.jobsModel.find({
          status: { $in: ["assigned"] },
          suitableDate: { $gte: now, $lte: timeFrame }
        }).populate("staffs").exec();
      };

      const upcomingOneHourJobs = await findJobs(oneHourTillNow);

      const upcomingFifteenMinutesJobs = await findJobs(fifteenMinutesTillNow);

      const upcomingOneDayJobs = await findJobs(oneDayTillNow);

      const upcomingThreeDaysJobs = await findJobs(threeDaysTillNow);

      if (upcomingFifteenMinutesJobs.length > 0) {
        for (const job of upcomingFifteenMinutesJobs) {
          for (const staff of job.staffs) {
            // TODO: Send notification based on language
            const notiMsg = getNotiMessage(staff?.lang);
            const jobReminderMsg = notiMsg["job-reminder-in-time"]("15 minutes");

            const isSentNoti = await this.notificationService.checkIfNotificationSent(staff._id.toString(), {
              title: jobReminderMsg.title,
              body: jobReminderMsg.body
            });
            if (isSentNoti) return;

            await this.notificationService.sendNotification(staff._id.toString(), {
              title: jobReminderMsg.title,
              body: jobReminderMsg.body,
              notificationType: NotificationType.REMINDER,
              data: {
                screen: MobileAppScreens.JobDetails,
                id: job._id.toString()
              }
            });
          }
        }
        return; // Prevent one hour notification sent if 15 minutes notification is sent
      }

      if (upcomingOneHourJobs.length > 0) {
        for (const job of upcomingOneHourJobs) {
          for (const staff of job.staffs) {
            // TODO: Send notification based on language
            const notiMsg = getNotiMessage(staff?.lang);
            const jobReminderMsg = notiMsg["job-reminder-in-time"]("1 hour");

            const isSentNoti = await this.notificationService.checkIfNotificationSent(staff._id.toString(), {
              title: jobReminderMsg.title,
              body: jobReminderMsg.body
            });
            if (!isSentNoti){
              await this.notificationService.sendNotification(staff._id.toString(), {
                title: jobReminderMsg.title,
                body: jobReminderMsg.body,
                notificationType: NotificationType.REMINDER,
                data: {
                  screen: MobileAppScreens.JobDetails,
                  id: job._id.toString()
                }
              });
            }
          }

          const adminNotiMsg = getNotiMessage("");
          const adminJobReminderMsg = adminNotiMsg["job-reminder-in-time"]("1 hour");
          const isSentNoti = await this.notificationService.checkIfAdminNotificationSent(job.workspace._id.toString(), {
            title: adminJobReminderMsg.title,
            body: adminJobReminderMsg.body,
            data: {
              screen: PortalScreens.Job,
              id: job._id.toString()
            },
          });
          if (isSentNoti) return;
          await this.notificationService.sendAdminNotification(job.workspace._id.toString(), {
            title: adminJobReminderMsg.title,
            body: adminJobReminderMsg.body,
            data: {
              screen: PortalScreens.Job,
              id: job._id.toString()
            },
          });
        }
      }

      if (upcomingOneDayJobs.length > 0) {
        for (const job of upcomingOneDayJobs) {
          const adminNotiMsg = getNotiMessage("");
          const adminJobReminderMsg = adminNotiMsg["job-reminder-in-time"]("1 day");
          const isSentNoti = await this.notificationService.checkIfAdminNotificationSent(job.workspace._id.toString(), {
            title: adminJobReminderMsg.title,
            body: adminJobReminderMsg.body,
            data: {
              screen: PortalScreens.Job,
              id: job._id.toString()
            },
          });
          if (isSentNoti) return;
          await this.notificationService.sendAdminNotification(job.workspace._id.toString(), {
            title: adminJobReminderMsg.title,
            body: adminJobReminderMsg.body,
            data: {
              screen: PortalScreens.Job,
              id: job._id.toString()
            },
          });
          // Send email to client
          const workspaceObject = await this.WorkspaceService.findOne(job.workspace._id.toString());
          const smtpConfig = await this.smtpConfigService.findLatest(job.workspace._id.toString());
          const companyEmail = smtpConfig.fromEmail || process.env.EMAIL;
          const companyName = process.env.COMPANY_NAME || "MaidProfit";
          const html = await ReminderEmail({
            logo: workspaceObject?.logo,
            companyEmail,
            companyName,
            clientName: job.bookingRequest.firstName + " " + job.bookingRequest.lastName,
            hoursUntilService: "1 day",
            serviceTime: dayjs(job.suitableDate).format("HH:mm"),
            currentYear: dayjs().format("YYYY")
          });
          await this.SendEmailService.sendEmailGoogle({
            from: smtpConfig.fromEmail,
            body: html,
            subject: "Reminder for upcoming service",
            toEmail: job.bookingRequest.email
          }, job.workspace._id.toString());
        }
      }

      if (upcomingThreeDaysJobs.length > 0) {
        for (const job of upcomingThreeDaysJobs) {
          const adminNotiMsg = getNotiMessage("");
          const adminJobReminderMsg = adminNotiMsg["job-reminder-in-time"]("3 days");
          const isSentNoti = await this.notificationService.checkIfAdminNotificationSent(job.workspace._id.toString(), {
            title: adminJobReminderMsg.title,
            body: adminJobReminderMsg.body,
            data: {
              screen: PortalScreens.Job,
              id: job._id.toString()
            },
          });
          if (isSentNoti) return;
          await this.notificationService.sendAdminNotification(job.workspace._id.toString(), {
            title: adminJobReminderMsg.title,
            body: adminJobReminderMsg.body,
            data: {
              screen: PortalScreens.Job,
              id: job._id.toString()
            },
          });
          // Send email to client
          const workspaceObject = await this.WorkspaceService.findOne(job.workspace._id.toString());
          const smtpConfig = await this.smtpConfigService.findLatest(job.workspace._id.toString());
          const companyEmail = smtpConfig.fromEmail || process.env.EMAIL;
          const companyName = process.env.COMPANY_NAME || "MaidProfit";
          const html = await ReminderEmail({
            logo: workspaceObject?.logo,
            companyEmail,
            companyName,
            clientName: job.bookingRequest.firstName + " " + job.bookingRequest.lastName,
            hoursUntilService: "3 days",
            serviceTime: dayjs(job.suitableDate).format("HH:mm"),
            currentYear: dayjs().format("YYYY")
          });
          await this.SendEmailService.sendEmailGoogle({
            from: smtpConfig.fromEmail,
            body: html,
            subject: "Reminder for upcoming service",
            toEmail: job.bookingRequest.email
          }, job.workspace._id.toString());
        }
      }
    } catch (err) {
      console.log("Error in cronScanIncomingJobs", err);
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  async updateExpiredCache() {
    const staleDate = dayjs().utc().startOf('day').toDate();
    staleDate.setDate(staleDate.getDate() - (this.CACHE_DURATION_DAYS / 2));

    const staleJobs = await this.jobsModel.find({
      parent: { $exists: false },
      lastCalculated: { $lt: staleDate }
    });

    for (const job of staleJobs) {
      if (!job.rrule) continue;
      const updatedJob = await this.updateNextOccurrences(job);
      await this.createChildJobs(updatedJob);
    }
  }

  async findAll(collectionDto: CollectionDto): Promise<any> {
    // Add parent filter to existing filter
    const filter = {
        ...collectionDto.filter,
        parent: { $exists: false }  // Only get parent jobs
    };

    return {
        data: await this.jobsModel
            .find(filter)
            .skip(collectionDto.page * collectionDto.limit)
            .limit(collectionDto.limit)
            .populate({
                path: "bookingRequest",
                populate: { path: "address" } // populate address in bookingRequest
            })
            .populate({
                path: "service",
                populate: { path: "clientType" } // populate address in bookingRequest
            })
            .populate({
                path: "frequency",
                populate: { path: "discountFrequency" } // populate address in bookingRequest
            })
            .populate("addons.addon")
            .populate("extras.extra")
            .populate("staffs")
            .sort(JSON.parse(collectionDto.sort || "{}"))
            .exec(),
        pagination: await this.paginate(collectionDto)
    };
  }

  async findAllByClientId(
    id: string,
    workspace: string,
    collectionDto: CollectionDto
  ): Promise<any> {
    const client = await this.ClientsService.getClient({
      id,
      workspace
    });


    if (!client) {
      throw new HttpException("Client not existed", HttpStatus.CONFLICT);
    }

    let mainEmail = client.email.find((i) => i.type == "main").item;
    // console.log(mainEmail);

    let jobs: any = await this.jobsModel.aggregate([
      {
        $lookup: {
          from: "bookingrequests",
          localField: "bookingRequest",
          foreignField: "_id",
          as: "bookingRequest"
        }
      },
      {
        $addFields: {
          email: { $first: "$bookingRequest.email" }
        }
      },
      {
        $match: {
          email: mainEmail,
          workspace: new mongoose.Types.ObjectId(workspace)
        }
      },
      {
        $lookup: {
          from: "bookingtypes",
          localField: "service",
          foreignField: "_id",
          as: "bookingTypes"
        }
      },
      {
        $addFields: {
          service: { $arrayElemAt: ["$bookingTypes.name", 0] } // Get the first item from the array
        }
      },
      {
        $addFields: {
          email: { $first: "$bookingRequest.email" }
        }
      },
      {
        $addFields: {
          title: {
            $concat: [
              { $first: "$bookingRequest.firstName" },
              " ",
              { $first: "$bookingRequest.lastName" },
            ]
          } // Get the first item from the array
        }
      },
      {
        $lookup: {
          from: "bookingfrequencies",
          localField: "frequency",
          foreignField: "_id",
          as: "frequency"
        }
      },
      {
        $lookup: {
          from: "frequencydiscounts",
          localField: "frequency.discountFrequency",
          foreignField: "_id",
          as: "frequency.discountFrequency"
        }
      },
      {
        $addFields: {
          frequency: { $arrayElemAt: ["$frequency.title", 0] } // Get the first item from the array
        }
      },
      {
        $facet: {
          // Get the paginated data
          data: [
            {
              $unwind: {
                path: "$addons",
                preserveNullAndEmptyArrays: true
              }
            },
            {
              $lookup: {
                from: "bookingaddons",
                localField: "addons.addon",
                foreignField: "_id",
                as: "addons.addonDetails"
              }
            },
            {
              $unwind: {
                path: "$addons.addonDetails",
                preserveNullAndEmptyArrays: true
              }
            },
            {
              $group: {
                _id: "$_id",
                addons: { $push: "$addons" },
                extras: { $first: "$extras" },
                title: { $first: "$title" },
                service: { $first: "$service" },
                frequency: { $first: "$frequency" },
                dateCreated: { $first: "$dateCreated" },
                status: { $first: "$status" },
                price: { $first: "$price" },
                email: { $first: "$email" }
              }
            },
            {
              $unwind: {
                path: "$extras",
                preserveNullAndEmptyArrays: true
              }
            },
            {
              $lookup: {
                from: "bookingextras",
                localField: "extras.extra",
                foreignField: "_id",
                as: "extras.extraDetails"
              }
            },
            {
              $unwind: {
                path: "$extras.extraDetails",
                preserveNullAndEmptyArrays: true
              }
            },
            {
              $group: {
                _id: "$_id",
                addons: { $first: "$addons" },
                extras: { $push: "$extras" },
                title: { $first: "$title" },
                service: { $first: "$service" },
                frequency: { $first: "$frequency" },
                dateCreated: { $first: "$dateCreated" },
                status: { $first: "$status" },
                price: { $first: "$price" },
                email: { $first: "$email" }
              }
            },

            // Pagination: Skip and Limit after the Group stage
            {
              $skip:
                (collectionDto?.page || 0) *
                (collectionDto?.limit || 50)
            },
            { $limit: collectionDto?.limit || 50 },

            {
              $project: {
                title: 1,
                service: 1,
                frequency: 1,
                dateCreated: 1,
                status: 1,
                price: 1,
                addons: {
                  $cond: {
                    if: { $eq: ["$addons", [{}]] }, // checks if array contains empty object
                    then: [], // if it is, replace it with an empty array
                    else: "$addons" // otherwise, leave it as is
                  }
                },
                extras: {
                  $cond: {
                    if: { $eq: ["$extras", [{}]] }, // checks if array contains empty object
                    then: [], // if it is, replace it with an empty array
                    else: "$extras" // otherwise, leave it as is
                  }
                }
              }
            }
          ],
          pagination: [
            {
              $count: "total"
            }
          ]
        }
      },
      {
        $addFields: {
          pagination: {
            $first: "$pagination"
          }
        }
      }
    ]);

    let result = jobs[0];

    if (!result.pagination) {
      result.pagination = {
        total: 0
      };
    }

    let pagination = paginateWithCount(
      collectionDto,
      result.pagination.total
    );

    return {
      result,
      pagination: {
        ...pagination
      }
    };
  }

  private createPagination(count: number, collectionDto: CollectionDto) {
    return {
      total: count,
      page: collectionDto.page,
      limit: collectionDto.limit,
      next:
        (collectionDto.page + 1) * collectionDto.limit >= count
          ? undefined
          : collectionDto.page + 1,
      prev: collectionDto.page == 0 ? undefined : collectionDto.page - 1
    };
  }

  private async paginate(query: CollectionDto) {
    const count: number = await this.count(query);
    return this.createPagination(count, query);
  }

  async count(query: CounterDto): Promise<number> {
    return this.jobsModel.countDocuments(query.filter).exec();
  }

  private formatRRuleString(rruleString: string): string {
    if (rruleString.includes('UNTIL=')) {
      const untilMatch = rruleString.match(/UNTIL=(\d{8})/);
      if (untilMatch && untilMatch[1]) {
        const year = untilMatch[1].substring(0, 4);
        const month = untilMatch[1].substring(4, 6);
        const day = untilMatch[1].substring(6, 8);
        
        const formattedDate = `${year}${month}${day}T000000Z`;
        
        rruleString = rruleString.replace(/UNTIL=\d{8}/, `UNTIL=${formattedDate}`);
      }
    }
    
    if (rruleString.includes('DTSTART=')) {
      const dtstartMatch = rruleString.match(/DTSTART=(\d{8})/);
      if (dtstartMatch && dtstartMatch[1]) {
        const year = dtstartMatch[1].substring(0, 4);
        const month = dtstartMatch[1].substring(4, 6);
        const day = dtstartMatch[1].substring(6, 8);
        
        const formattedDate = `${year}${month}${day}T000000Z`;
        
        rruleString = rruleString.replace(/DTSTART=\d{8}/, `DTSTART=${formattedDate}`);
      }
    }
    
    return rruleString;
  }

  /**
   * Gets jobs data based on recurring rule for calendar view with pagination
   * Pagination is applied only for search and status filters, not for calendar weekly/daily views
   * Default limit is 10 and default page is 0 if not specified for search and status filters
   * No pagination is applied for calendar view when limit and page are undefined
   */
  async complexByRruleString(collectionDto: CollectionDto): Promise<any> {
    let data = [];

    if (!collectionDto?.filter) {
      throw new HttpException(
        "filter object is require",
        HttpStatus.CONFLICT
      );
    }

    if (!collectionDto.filter?.rrule) {
      throw new HttpException(
        "rrule string is require",
        HttpStatus.CONFLICT
      );
    }

    if (!collectionDto.filter?.timezone) {
      throw new HttpException(
        "timezone string is require",
        HttpStatus.CONFLICT
      );
    }

    const page = typeof collectionDto.page === 'number' ? collectionDto.page : undefined;
    const limit = typeof collectionDto.limit === 'number' ? collectionDto.limit : undefined;

    const hasSearchFilter = typeof collectionDto.filter?.search === 'string' && collectionDto.filter.search.trim() !== '';
    const hasStatusFilter = typeof collectionDto.filter?.status === 'string' && collectionDto.filter.status.trim() !== '';
    
    const timezoneCode = collectionDto.filter.timezone;
    
    const rruleString = this.formatRRuleString(collectionDto.filter.rrule.toString());
    const rule = rrulestr(rruleString);
    const datesQuery = rule.all();
    console.log("datesQuery", datesQuery);

    if (!datesQuery?.length) {
      throw new HttpException(
        "rrule string is not correct",
        HttpStatus.CONFLICT
      );
    }

    const shouldCheckDateRange = !hasSearchFilter && !hasStatusFilter;
    const filterTodayOnly = collectionDto.filter.today === true;
    
    data = await Promise.all(datesQuery.map(async (date) => {
      let currentDay, endDay;
      
      if (filterTodayOnly) {
        const todayInClientTZ = dayjs().tz(timezoneCode as string);
        const todayStartInClientTZ = todayInClientTZ.startOf('day');
        const todayEndInClientTZ = todayInClientTZ.endOf('day');
        
        currentDay = todayStartInClientTZ.utc().toDate();
        endDay = todayEndInClientTZ.utc().toDate();
      } else {
        const dateInClientTZ = dayjs(date).tz(timezoneCode as string);
        const dayStartInClientTZ = dateInClientTZ.startOf('day');
        const dayEndInClientTZ = dateInClientTZ.endOf('day');
        
        currentDay = dayStartInClientTZ.toDate();
        endDay = dayEndInClientTZ.toDate();
      }

      const aggregateQuery: PipelineStage[] = [
        {
          $match: {
            workspace: collectionDto.filter.workspace,
            // parent: { $exists: false }
            suitableDate: {
              $gte: currentDay,
              $lte: endDay
            }
          }
        },
        {
          $lookup: {
            from: "bookingfrequencies",
            localField: "frequency",
            foreignField: "_id",
            as: "frequency"
          }
        },
        {
          $lookup: {
            from: "frequencydiscounts",
            localField: "frequency.discountFrequency",
            foreignField: "_id",
            as: "frequency.discountFrequency"
          }
        },
        {
          $lookup: {
            from: "bookingtypes",
            localField: "service",
            foreignField: "_id",
            as: "bookingTypes"
          }
        },
        {
          $lookup: {
            from: "bookingrequests",
            localField: "bookingRequest",
            foreignField: "_id",
            as: "request"
          }
        },
        {
          $lookup: {
            from: "users",
            localField: "staffs",
            foreignField: "_id",
            as: "staffData"
          }
        },
        {
          $lookup: {
            from: "users",
            localField: "staffStatus.staff",
            foreignField: "_id",
            as: "staffStatusData"
          }
        },
        {
          $addFields: {
            frequencyDate: {
              $first: "$frequency.frequency"
            }
          }
        },
        {
          $addFields: {
            hour: {
              $hour: {
                date: "$suitableDate",
                timezone: "UTC"
              }
            }
          }
        },
        {
          $addFields: {
            min: {
              $minute: {
                date: "$suitableDate",
                timezone: "UTC"
              }
            }
          }
        },
        {
          $addFields: {
            startUTC: {
              $dateFromString: {
                dateString: {
                  $concat: [
                    { $dateToString: { date: currentDay, format: "%Y-%m-%d" } },
                    "T",
                    { $toString: "$hour" },
                    ":",
                    { $toString: "$min" },
                    ":00Z"
                  ]
                }
              }
            }
          }
        },
        {
          $addFields: {
            start: "$suitableDate"
          }
        },
        {
          $addFields: {
            endCheck: {
              $add: [
                "$start",
                { $multiply: [1000 * 60 * 60, { $divide: [{ $first: "$request.duration" }, 60] }] },
                { $multiply: [1000 * 60 * 60, { $divide: ["$hours", 60] }] }
              ]
            }
          }
        },
        {
          $addFields: {
            end: {
              $cond: {
                if: { $eq: ["$endCheck", null] },
                then: {
                  $add: [
                    "$start",
                    { $multiply: [1000 * 60 * 60, { $divide: [{ $ifNull: [{ $first: "$request.duration" }, 120] }, 60] }] },
                    { $multiply: [1000 * 60 * 60, { $divide: [{ $ifNull: ["$hours", 0] }, 60] }] }
                  ]
                },
                else: "$endCheck"
              }
            }
          }
        },
        {
          $addFields: {
            addressId: {
              $toObjectId: {
                $first: "$request.address"
              }
            }
          }
        },
        {
          $lookup: {
            from: "addresses",
            localField: "addressId",
            foreignField: "_id",
            as: "addressContent"
          }
        },
        {
          $set: {
            _id: "$_id",
            title: {
              $concat: [
                { $first: "$request.firstName" },
                " ",
                { $first: "$request.lastName" },
              ]
            },
            address: {
              $concat: [
                { $first: "$addressContent.street_one" },
                ", ",
                { $first: "$addressContent.city" },
                ", ",
                { $first: "$addressContent.state" },
              ]
            },
            service: { $first: "$bookingTypes.name" },
            serviceId: { $first: "$bookingTypes._id" },
            sentDate: "$dateCreated",
            status: "$status",
            frequency: { $first: "$frequency.title" },
            price: "$price",
            dateCreated: "$dateCreated",
            staffs: {
              $cond: {
                if: { $gt: [{ $size: "$staffData" }, 0] },
                then: {
                  $map: {
                    input: "$staffData",
                    as: "staff",
                    in: {
                      _id: "$$staff._id",
                      firstName: "$$staff.firstName",
                      lastName: "$$staff.lastName",
                      fullName: {
                        $concat: ["$$staff.firstName", " ", "$$staff.lastName"]
                      }
                    }
                  }
                },
                else: {
                  $cond: {
                    if: { $gt: [{ $size: "$staffStatusData" }, 0] },
                    then: {
                      $map: {
                        input: "$staffStatusData",
                        as: "staff",
                        in: {
                          _id: "$$staff._id",
                          firstName: "$$staff.firstName",
                          lastName: "$$staff.lastName",
                          fullName: {
                            $concat: ["$$staff.firstName", " ", "$$staff.lastName"]
                          }
                        }
                      }
                    },
                    else: []
                  }
                }
              }
            }
          }
        },
        {
          $unset: [
            "bookingRequest",
            "request",
            "addons",
            "extras",
            "video",
            "attachments",
            "__v",
            "bookingTypes",
            "startUTC",
            "hour",
            "endCheck",
            "clientTypeId",
            "addressId",
            "addressContent",
            "clientTypeContent",
            "sentDate",
            "price",
            "dateCreated",
            "staffData",
            "staffStatusData"
          ]
        },
      ]

      // Add search condition if exists
      if (collectionDto.filter?.search && typeof collectionDto.filter.search === 'string' && collectionDto.filter.search.trim() !== '') {
        const searchTerm = collectionDto.filter.search.trim();
        const searchRegex = { $regex: searchTerm, $options: 'i' };

        aggregateQuery.push({
          $match: {
            $or: [
              { title: searchRegex },
              { address: searchRegex },
              { service: searchRegex },
              { status: searchRegex },
              { 'request.firstName': searchRegex },
              { 'request.lastName': searchRegex },
              { 'request.email': searchRegex },
              { 'request.phone': searchRegex },
              { 'addressContent.street_one': searchRegex },
              { 'addressContent.city': searchRegex },
              { 'addressContent.state': searchRegex },
              { 'addressContent.zip': searchRegex },
              { 'bookingTypes.name': searchRegex },
              { notes: searchRegex },
              // Search in staffs array
              { 'staffs.firstName': searchRegex },
              { 'staffs.lastName': searchRegex },
              { 'staffs.fullName': searchRegex }
            ]
          }
        });
      }

      // Add service filter if exists
      if (collectionDto.filter?.service && typeof collectionDto.filter.service === 'string') {
        aggregateQuery.push({
          $match: {
            serviceId: new mongoose.Types.ObjectId(collectionDto.filter.service)
          }
        });
      }

      // Add staff filter if exists
      if (collectionDto.filter?.staff && typeof collectionDto.filter.staff === 'string') {
        aggregateQuery.push({
          $match: {
            'staffs._id': new mongoose.Types.ObjectId(collectionDto.filter.staff)
          }
        });
      }

      // Add status condition if exists
      if (collectionDto.filter?.status && typeof collectionDto.filter.status === 'string' && collectionDto.filter.status.trim() !== '') {
        aggregateQuery.push({
          $match: {
            status: collectionDto.filter.status
          }
        });
      }
      // Only check date range if no search/status filters or if filtering for today
      else if (shouldCheckDateRange || filterTodayOnly) {
        aggregateQuery.push({
          $match: {
            $or: [
              {
                nextOccurrences: {
                  $elemMatch: {
                    $gte: currentDay,
                    $lte: endDay
                  }
                }
              },
              {
                suitableDate: {
                  $gte: currentDay,
                  $lte: endDay
                }
              }
            ]
          }
        });
      }

      const result = await this.jobsModel.aggregate(aggregateQuery).exec();

      const jobStatusAggrQuery = [
        ...aggregateQuery,
        {
          $group: {
            _id: "$status",
            count: { $sum: 1 },
          },
        },
        {
          $replaceWith: {
            status: "$_id",
            count: "$count",
          },
        },
      ]

      const jobStatus = await this.jobsModel.aggregate(jobStatusAggrQuery).exec();
      return {
        date,
        jobStatus,
        result  
      };
    }));

    if (filterTodayOnly) {
      const todayStart = dayjs().tz(timezoneCode as string).startOf('day');
      const todayEnd = dayjs().tz(timezoneCode as string).endOf('day');
      
      data = data.filter(item => {
        const itemDateInClientTZ = dayjs(item.date).tz(timezoneCode as string);
        return itemDateInClientTZ >= todayStart && itemDateInClientTZ <= todayEnd;
      });
    }

    const dataRes = await Promise.all(data);
    
    // Get all job data from all dates
    let jobData = dataRes.reduce((acc, curr) => {
      return [...acc, ...curr.result];
    }, []);
    
    // Get all job status from all dates
    const jobStatus = dataRes.reduce((acc, curr) => {
      return [...acc, ...curr.jobStatus];
    }, []);

    // Calculate total count for pagination
    const totalCount = jobData.length;

    let paginatedData = jobData;
    let pagination = null;

    if ((hasSearchFilter || hasStatusFilter) && page !== undefined && limit !== undefined) {
      paginatedData = jobData.slice(
        page * limit,
        (page + 1) * limit
      );

      pagination = {
        total: totalCount,
        page: page,
        limit: limit,
        next: (page + 1) * limit >= totalCount ? undefined : page + 1,
        prev: page == 0 ? undefined : page - 1
      };
    }

    if (pagination) {
      return {
        data: paginatedData,
        jobStatus,
        pagination
      };
    }
    return {
      data: paginatedData,
      jobStatus
    };
  }

  async complexByRruleStringCalc(collectionDto: CollectionDto): Promise<any> {
    let data = [];
    let query: any = [
      {
        $match: {
          // parent: { $exists: false }
        }
      },
      {
        $sort: { dateCreated: 1 }
      }
    ];
    if (!collectionDto?.filter) {
      throw new HttpException(
        "filter object is require",
        HttpStatus.CONFLICT
      );
    }

    if (!collectionDto.filter?.rrule) {
      throw new HttpException(
        "rrule string is require",
        HttpStatus.CONFLICT
      );
    }
    
    if (!collectionDto.filter?.timezone) {
      collectionDto.filter.timezone = "UTC";
    }
    
    const timezoneCode = collectionDto.filter.timezone as string;

    if (collectionDto.filter?.status) {
      query = [
        ...query,
        {
          $match: {
            $and: [
              { status: collectionDto.filter.status },
              { workspace: collectionDto.filter.workspace },
              // { parent: { $exists: false } }
            ]
          }
        }
      ];
    }

    const rruleString = this.formatRRuleString(collectionDto.filter.rrule.toString());
    const rule = rrulestr(rruleString);
    const datesQuery = rule.all();

    if (!datesQuery?.length) {
      throw new HttpException(
        "rrule string is not correct",
        HttpStatus.CONFLICT
      );
    }
    data = await Promise.all(
      datesQuery.map(async (date) => {
        let result = await this.jobsModel
          .aggregate([
            // {
            //   $match: {
            //     parent: { $exists: false }
            //   }
            // },
            {
              $lookup: {
                from: "bookingfrequencies",
                localField: "frequency",
                foreignField: "_id",
                as: "frequency"
              }
            },
            {
              $lookup: {
                from: "frequencydiscounts",
                localField: "frequency.discountFrequency",
                foreignField: "_id",
                as: "frequency.discountFrequency"
              }
            },
            {
              $lookup: {
                from: "bookingtypes",
                localField: "service",
                foreignField: "_id",
                as: "bookingTypes"
              }
            },
            {
              $lookup: {
                from: "bookingrequests",
                localField: "bookingRequest",
                foreignField: "_id",
                as: "request"
              }
            },
            {
              $addFields: {
                frequencyDate: {
                  $first: "$frequency.frequency"
                }
              }
            },
            {
              $set: {
                _id: "$_id",
                title: {
                  $concat: [
                    { $first: "$request.firstName" },
                    " ",
                    { $first: "$request.lastName" },
                  ]
                },
                services: { $first: "$bookingTypes.name" },
                clientType: {
                  $first: "$bookingTypes.clientType"
                },
                sentDate: "$suitableDate",
                status: "$status",
                frequency: { $first: "$frequency.title" },
                price: "$price",
                dateCreated: "$dateCreated",
                staffs: {
                  $cond: {
                    if: { $gt: [{ $size: "$staffData" }, 0] },
                    then: {
                      $map: {
                        input: "$staffData",
                        as: "staff",
                        in: {
                          _id: "$$staff._id",
                          firstName: "$$staff.firstName",
                          lastName: "$$staff.lastName",
                          fullName: {
                            $concat: ["$$staff.firstName", " ", "$$staff.lastName"]
                          }
                        }
                      }
                    },
                    else: {
                      $ifNull: [
                        {
                          $map: {
                            input: "$staffStatus",
                            as: "status",
                            in: { _id: "$$status.staff" }
                          }
                        },
                        []
                      ]
                    }
                  }
                }
              }
            },
            {
              $unset: [
                "bookingRequest",
                "request",
                "addons",
                "extras",
                "staffs",
                "video",
                "attachments",
                "suitableDate",
                "__v",
                "bookingTypes",
                "notes",
                "service"
              ]
            },
            ...query
          ])
          .exec();
        return result;
      })
    );

    return data.flat();
  }

  async getJobOccurrences(
    jobId: string,
    start: Date,
    end: Date
  ): Promise<Date[]> {
    const job = await this.jobsModel.findById(jobId);
    if (!job) {
      throw new Error('Job not found');
    }

    const parentId = job.parent ? job.parent : jobId;
    const parentJob = job.parent ? await this.jobsModel.findById(parentId) : job;
    
    if (!parentJob) {
      throw new Error('Parent job not found');
    }
    
    if (!parentJob.rrule) {
      if (job.suitableDate >= start && job.suitableDate <= end) {
        return [job.suitableDate];
      }
      return [];
    }

    const rule = RRule.fromString(parentJob.rrule);
    return rule.between(start, end, true)
      .filter(date => !this.isExcluded(date, parentJob.excludedDates));
  }

  async findUpcomingJobs(
    startDate: Date = dayjs().utc().startOf('day').toDate(),
    limit: number = 10
  ): Promise<Jobs[]> {
    const jobs = await this.jobsModel.find({
      nextOccurrences: {
        $elemMatch: {
          $gte: startDate,
        },
      },
    })
      .sort({ 'nextOccurrences.0': 1 })
      .limit(limit)
      .exec();

    // Update cache if needed
    for (const job of jobs) {
      if (this.shouldUpdateCache(job)) {
        await this.updateNextOccurrences(job);
      }
    }

    return jobs;
  }

  async findOne(id: string, workspace: string): Promise<any> {
    const job = await this.jobsModel
      .findOne({
        _id: id,
        workspace: new mongoose.Types.ObjectId(workspace),
      })
      .populate({
        path: 'bookingRequest',
        populate: [
          { path: 'address' },
          { path: 'service' }
        ]
      })
      .populate({
        path: 'service',
        populate: { path: 'clientType' }
      })
      .populate({
        path: 'addons',
        populate: { path: 'addon' }
      })
      .populate({
        path: 'extras',
        populate: { path: 'extra' }
      })
      .populate({
        path: 'frequency',
        populate: { path: 'discountFrequency' }
      })
      .populate('staffs')
      .populate({
        path: 'staffMedias',
        populate: { path: 'medias' }
      })
      .populate('workspace');

    if (!job) {
      throw new HttpException('Job not found', HttpStatus.NOT_FOUND);
    }

    // Get full invoice information
    const invoice = await this.invoicesService.findByJobId(id, workspace);
    
    const jobData = job.toObject();

    let addons = jobData.addons;
    let extras = jobData.extras;
    let addonsQuery = [];
    let extrasQuery = [];

    if (addons) {
      addonsQuery = await Promise.all(addons.map(async (addon) => {
        const svgContent = await this.mediasService.getMedia(addon.addon.icon);
        addon.addon = {
          ...addon.addon,
          //@ts-ignore
          iconSVG: await svgContent.Body.transformToString()
        }
        return {
          ...addon
        }
      }));
    }

    if (extras) {
      extrasQuery = await Promise.all(extras.map(async (extra) => {
        const svgContent = await this.mediasService.getMedia(extra.extra.icon);
        extra.extra = {
          ...extra.extra,
          //@ts-ignore
          iconSVG: await svgContent.Body.transformToString()
        }
        return {
          ...extra
        }
      }));
    }

    jobData.addons = addonsQuery;
    jobData.extras = extrasQuery;

    return {
      ...jobData,
      invoiceScheduling: invoice || null // Return full invoice object or null
    };
  }

  async findByBookingRequestId(id: string, workspace: string): Promise<Jobs> {
    return await this.jobsModel
      .findOne({
        bookingRequest: new mongoose.Types.ObjectId(id),
        workspace: new mongoose.Types.ObjectId(workspace),
        parent: {
          $exists: false
        }
      })
      .populate({
        path: 'bookingRequest',
        populate: [
          { path: 'address' },
          { path: 'service' }
        ]
      })
      .populate({
        path: 'service',
        populate: { path: 'clientType' }
      })
      .populate({
        path: 'addons',
        populate: { path: 'addon' }
      })
      .populate({
        path: 'extras',
        populate: { path: 'extra' }
      })
      .populate({
        path: 'frequency',
        populate: { path: 'discountFrequency' }
      })
      .populate('staffs')
      .populate({
        path: 'staffMedias',
        populate: { path: 'medias' }
      })
      .populate('workspace')
      .exec();
  }

  async findDuplicateJob(job: CreateJobDto): Promise<Jobs | null> {
    const bookingRequestId = typeof job.bookingRequest === 'string' ? 
      job.bookingRequest : 
      job.bookingRequest._id;

    const serviceId = typeof job.service === 'string' ? 
      job.service : 
      job.service._id;

    const workspaceId = typeof job.workspace === 'string' ? 
      job.workspace : 
      job.workspace;

    return await this.jobsModel.findOne({
      bookingRequest: new mongoose.Types.ObjectId(bookingRequestId),
      service: new mongoose.Types.ObjectId(serviceId),
      suitableDate: job.suitableDate,
      workspace: new mongoose.Types.ObjectId(workspaceId)
    })
    .populate({
      path: "bookingRequest",
      populate: [
        { path: "address" },
        { path: "service" }
      ]
    })
    .populate({
      path: "service",
      populate: { path: "clientType" }
    })
    .populate({
      path: "frequency",
      populate: { path: "discountFrequency" }
    })
    .populate("addons.addon")
    .populate("extras.extra")
    .populate("staffMedias.medias")
    .populate("staffs")
    .populate("workspace");
  }

  async create(job: CreateJobDto): Promise<Jobs> {
    let dateCreated = dayjs().utc().startOf('day').toDate();

    // Extract IDs from objects if they are objects
    const bookingRequestId = typeof job.bookingRequest === 'string' ? 
      job.bookingRequest : 
      job.bookingRequest._id;

    const serviceId = typeof job.service === 'string' ? 
      job.service : 
      job.service._id;

    const workspaceId = typeof job.workspace === 'string' ? 
      job.workspace : 
      job.workspace;

    const freq = await this.bookingFrequencyService.findOne(job.frequency, workspaceId);

    if (!freq) {
      throw new HttpException("Frequency is not existed", HttpStatus.NOT_FOUND);
    }

    const existingJob = await this.jobsModel.findOne({
      bookingRequest: bookingRequestId,
      service: serviceId,
      suitableDate: job.suitableDate,
      workspace: workspaceId,
    });
  
    if (existingJob) {
      return existingJob;
    }

    // Transform addons array if needed
    const transformedAddons = job.addons?.map(addon => ({
      quantity: addon.quantity,
      addon: typeof addon.addon === 'string' ? addon.addon : addon.addon._id
    }));

    // Transform extras array if needed  
    const transformedExtras = job.extras?.map(extra => ({
      quantity: extra.quantity,
      extra: typeof extra.extra === 'string' ? extra.extra : extra.extra._id
    }));

    const rrule = this.createRRule(freq, job.suitableDate, job.endDate, job.timezone);
    const newJob = await this.jobsModel.create({
      bookingRequest: new mongoose.Types.ObjectId(bookingRequestId),
      service: new mongoose.Types.ObjectId(serviceId),
      addons: transformedAddons || [],
      extras: transformedExtras || [],
      frequency: new mongoose.Types.ObjectId(job.frequency),
      staffs: job.staffs,
      video: job.video,
      attachments: job.attachments,
      suitableDate: job.suitableDate,
      status: job.status || "unassigned",
      workspace: new mongoose.Types.ObjectId(workspaceId),
      dateCreated: dateCreated,
      price: job.price,
      hours: job.hours,
      discountCode: job.discountCode,
      square_ft: job.square_ft,
      rrule: rrule,
      nextOccurrences: [],
      lastCalculated: dayjs().utc().startOf('day').toDate(),
      endDate: job.endDate,
      excludedDates: job.endDate ? [job.endDate] : [],
      timezone: job.timezone,
    });

    await newJob.populate({
      path: 'frequency',
      populate: 'discountFrequency'
    });

    const created = await this.updateNextOccurrences(newJob);

    if (created) {
      // Create children jobs
      await this.createChildJobs(created);
      return created;
    }
    throw new BadRequestException(`Failed to create new job, try again`);
  }

  async update(id: string, job: UpdateJobDto, userId?: string): Promise<any> {
    const checkExist = await this.jobsModel.findOne({
      _id: id,
      // parent: {
      //   $exists: false
      // }
    });
    if (!checkExist) {
      throw new HttpException("Job is not existed", HttpStatus.CONFLICT);
    }

    let updateData = { ...job };

    ["service", "frequency", "workspace", "bookingRequest"].forEach(key => {
      if (job?.[key]) {
        updateData[key] = new mongoose.Types.ObjectId(job[key]);
      }
    });

    if (job.status === "completed") {
      updateData["dateServiced"] = dayjs().utc().startOf('day').toDate();
    }

    if (checkExist.frequency.toString() !== job.frequency) {
      const freq = await this.bookingFrequencyService.findOne(job.frequency, job.workspace);
      if (!freq) {
        throw new HttpException("Frequency is not existed", HttpStatus.NOT_FOUND);
      }
      updateData["rrule"] = this.createRRule(freq, job.suitableDate, job.endDate);
      updateData["lastCalculated"] = dayjs().utc().startOf('day').toDate();
    }

    const currentStaffs = checkExist.staffs.map((staff) => staff.toString() || "");
    const newStaffs = job.staffs.map((staff) => staff || "");

    const newlyAddedStaffs = newStaffs.filter((staff) => !currentStaffs.includes(staff));
    const removedStaffs = currentStaffs.filter((staff) => !newStaffs.includes(staff));

    if (newlyAddedStaffs.length > 0) {
      for (const staff of newlyAddedStaffs) {
        updateData['staffStatus'] = [...(checkExist['staffStatus'] || []), {
          staff: new mongoose.Types.ObjectId(staff),
          updatedAt: dayjs.utc().toDate(),
          status: "assigned"
        }]
      }
      updateData.staffs = newStaffs;
    }
    if (removedStaffs.length > 0) {
      for (const staff of removedStaffs) {
        updateData['staffStatus'] = checkExist['staffStatus']?.filter((status) => status.staff.toString() !== staff);
      }
      updateData.staffs = newStaffs;
    }

    const updated = await this.jobsModel.findByIdAndUpdate(
      id,
      { ...updateData },
      { new: true, runValidators: true }
    )
      .populate("bookingRequest")
      .populate({
        path: "bookingRequest",
        populate: { path: "arrival" } // populate address in bookingRequest
      })
      .populate({
        path: 'frequency',
        populate: 'discountFrequency'
      })
      .populate("staffs")
      .lean();

    const jobId = checkExist.parent || checkExist._id;


    const childJobs = await this.jobsModel.find({
      $or: [
        { parent: jobId },
        { _id: jobId }
      ],
      _id: { $ne: checkExist._id },
      status: {
        $in: ["assigned"]
      }
    }).exec();

    if (newlyAddedStaffs.length > 0) {
      const newlyAddedStaffsIds = newlyAddedStaffs.map((staff) => staff.toString());
      if (childJobs.length > 0) {
        for (const childJob of childJobs) {
          const currentStaffs = childJob.staffs.map((staff) => staff.toString() || "");
          const newStaffs = [...currentStaffs, ...newlyAddedStaffsIds];
          await this.jobsModel.findByIdAndUpdate(childJob._id, {
            staffs: newStaffs
          }).exec();
        }
      }

      // TODO: Send notification based on language
      const notiMsg = getNotiMessage("");
      await this.notificationService.sendMulticastNotification(newlyAddedStaffs, {
        title: notiMsg["assigned-job"].title,
        body: notiMsg["assigned-job"].body,
        data: {
          screen: MobileAppScreens.JobDetails,
          id: updated._id.toString()
        }
      });
    }
    if (removedStaffs.length > 0) {
      if (childJobs.length > 0) {
        for (const childJob of childJobs) {
          const currentStaffs = childJob.staffs.map((staff) => staff.toString() || "");
          const newStaffs = currentStaffs.filter((staff) => !removedStaffs.includes(staff));
          await this.jobsModel.findByIdAndUpdate(childJob._id, {
            staffs: newStaffs
          }).exec();
        }
      }

      // TODO: Send notification based on language
      const notiMsg = getNotiMessage("");
      await this.notificationService.sendMulticastNotification(removedStaffs, {
        title: notiMsg["unassigned-job"].title,
        body: notiMsg["unassigned-job"].body,
        data: {
          screen: MobileAppScreens.JobDetails
        }
      });
    }

    const updatedJob = await this.updateNextOccurrences(updated);


    if (updated) {
      if (updatedJob && !updatedJob.parent) {
        await this.updateChildJobs(updatedJob);
        return updatedJob;
      }
      return updated;
    }
    return updated;
  }

  async delete(id: string, workspace: string): Promise<Jobs> {
    // Check if job exists (can be either parent or child job)
    const job = await this.jobsModel.findOne({
      _id: id,
      workspace: new mongoose.Types.ObjectId(workspace)
    });
    
    if (!job) {
      throw new HttpException("Job not found", HttpStatus.NOT_FOUND);
    }
    
    try {
      // Case 1: Job is a parent job (has no parent)
      if (!job.parent) {
        // Delete related invoice
        const invoiceRelated = await this.invoicesService.findByJobId(id, workspace);
        if (invoiceRelated) {
          await this.invoicesService.delete(invoiceRelated._id.toString(), workspace);
        }
        
        // Delete all children jobs
        await this.jobsModel.deleteMany({
          parent: id
        }).exec();
        
        // Delete the parent job
        return await job.deleteOne();
      } 
      // Case 2: Job is a child job (has parent)
      else {
        // Find parent job and update it
        const parentJob = await this.jobsModel.findById(job.parent);
        
        if (parentJob) {
          const jobDate = this.normalizeDate(job.suitableDate);
          
          // Add the date to parent job's excluded dates if not already there
          const isAlreadyExcluded = parentJob.excludedDates.some(excl => 
            this.normalizeDate(excl).getTime() === jobDate.getTime()
          );
          
          if (!isAlreadyExcluded) {
            parentJob.excludedDates.push(new Date(jobDate));
            
            // Remove the date from parent's next occurrences
            parentJob.nextOccurrences = parentJob.nextOccurrences.filter(occ => 
              this.normalizeDate(occ).getTime() !== jobDate.getTime()
            );
            
            await parentJob.save();
          }
        }
        
        // Delete child job-related invoices
        const invoiceRelated = await this.invoicesService.findByJobId(id, workspace);
        if (invoiceRelated) {
          await this.invoicesService.delete(invoiceRelated._id.toString(), workspace);
        }
        
        // Delete the child job
        return await job.deleteOne();
      }
    } catch (error) {
      throw new HttpException(
        `Failed to delete job: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async cancelOccurrence(jobId: string, date: Date): Promise<void> {
    try {
      // Find job by id
      const job = await this.jobsModel.findById(jobId);
      if (!job) {
        throw new Error('Job not found');
      }

      // Normalize date to start of day to avoid time comparison issues
      const normalizedDate = this.normalizeDate(date);
      
      // Case 1: Job is a parent/recurring job
      if (!job.parent) {
        // Ensure the date exists in nextOccurrences
        const occurrenceExists = job.nextOccurrences.some(occ => 
          this.normalizeDate(occ).getTime() === normalizedDate.getTime()
        );

        if (!occurrenceExists) {
          throw new Error(`Occurrence for date ${normalizedDate.toISOString().split('T')[0]} not found in job schedule`);
        }

        // Add the date to excluded dates if not already there
        const isAlreadyExcluded = job.excludedDates.some(excl => 
          this.normalizeDate(excl).getTime() === normalizedDate.getTime()
        );

        if (!isAlreadyExcluded) {
          job.excludedDates.push(new Date(normalizedDate));
        }
        
        // Remove the date from next occurrences
        job.nextOccurrences = job.nextOccurrences.filter(occ => 
          this.normalizeDate(occ).getTime() !== normalizedDate.getTime()
        );

        // Delete child job for this date if it exists
        const startOfDay = new Date(normalizedDate);
        startOfDay.setHours(0, 0, 0, 0);
        
        const endOfDay = new Date(normalizedDate);
        endOfDay.setHours(23, 59, 59, 999);
        
        await this.jobsModel.deleteOne({
          parent: job._id,
          suitableDate: {
            $gte: startOfDay,
            $lt: endOfDay
          }
        }).exec();

        await job.save();
        return;
      } 
      
      // Case 2: Job is a child job, we need to update the parent
      else {
        // Find the parent job
        const parentJob = await this.jobsModel.findById(job.parent);
        if (!parentJob) {
          throw new Error('Parent job not found');
        }
        
        // Add the date to excluded dates if not already there
        const isAlreadyExcluded = parentJob.excludedDates.some(excl => 
          this.normalizeDate(excl).getTime() === normalizedDate.getTime()
        );

        if (!isAlreadyExcluded) {
          parentJob.excludedDates.push(new Date(normalizedDate));
        }
        
        // Remove the date from next occurrences
        parentJob.nextOccurrences = parentJob.nextOccurrences.filter(occ => 
          this.normalizeDate(occ).getTime() !== normalizedDate.getTime()
        );
          
        // Save the parent job
        await parentJob.save();
        
        // Delete this child job
        await this.jobsModel.deleteOne({ _id: job._id }).exec();
        return;
      }
    } catch (error) {
      throw error;
    }
  }

  async findNearestToFarthestCleaners(clientEmail: string): Promise<any[]> {
    try {
      const foundClientGeoAndRole = await this.clientDataModel.aggregate([
        {
          $match: {
            email: {
              $elemMatch: {
                type: "main",
                item: clientEmail
              }
            }
          }
        },
        {
          $project: {
            address: 1
          }
        },
        {
          $lookup: {
            from: "addresses",
            let: { address: "$address" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [{ $eq: ["$_id", "$$address"] }]
                  }
                }
              },
              {
                $project: {
                  coordinates: 1
                }
              }
            ],
            as: "address_details"
          }
        },
        {
          $lookup: {
            from: "roles",
            pipeline: [
              {
                $match: {
                  name: "Cleaner"
                }
              },
              {
                $addFields: {
                  id: { $toString: "$_id" }
                }
              },
              {
                $unset: "_id"
              },
              {
                $project: {
                  id: 1
                }
              }
            ],
            as: "cleaner_role_id"
          }
        }
      ]);
      if (foundClientGeoAndRole.length <= 0)
        throw new BadRequestException(
          `Not found the provided client data`
        );

      const foundCleaners = await this.usersModel.find({
        role: foundClientGeoAndRole[0]?.cleaner_role_id[0]?.id,
        "address.coordinates": {
          $nearSphere: {
            $geometry: {
              type: "Point",
              coordinates:
              foundClientGeoAndRole[0]?.address_details[0]
                ?.coordinates
            }
          }
        }
      });

      return foundCleaners;
    } catch (error) {
      throw new BadRequestException(
        `Failed to find nearest cleaners, try again.`
      );
    }
  }

  async findAssignedCleaners(id: string): Promise<any[]> {
    try {
      const foundAssignedCleaners = await this.jobsModel.aggregate([
        {
          $match: {
            $expr: {
              $and: [
                {
                  $eq: ["$_id", { $toObjectId: id }]
                },
                {
                  $eq: ["$status", "unassigned"]
                }
              ]
            }
          }
        },
        {
          $project: {
            _id: 1,
            staffs: 1
          }
        },
        {
          $lookup: {
            from: "users",
            let: { staffs: "$staffs" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $in: ["$_id", "$$staffs"]
                  }
                }
              },
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  username: 1,
                  email: 1,
                  workspace: 1
                }
              },
              {
                $addFields: {
                  title: "Class Cleaner"
                }
              }
            ],
            as: "assigned_cleaners"
          }
        }
      ]);
      return foundAssignedCleaners;
    } catch (error) {
      throw new BadRequestException(
        `Failed to find assigned cleaners, try again.`
      );
    }
  }

  async findOutOfJobWorkingHoursCleaner(id: string): Promise<any[]> {
    try {
      const foundJob = await this.jobsModel.findById(id).exec();
      if (!foundJob) return;
      const foundCleaners = await this.workingHoursModel
        .find({
          job: foundJob.id,
          workspace: foundJob.workspace,
          suitableDate: {
            $gt: foundJob.suitableDate,
            $lt: foundJob.suitableDate
          },
          start_time: {
            $lt: foundJob.bookingRequest.arrival.timeStart
          },
          end_time: { $gt: foundJob.bookingRequest.arrival.timeEnd }
        })
        .sort({
          suitableDate: 1
        })
        .lean()
        .exec();
      console.log(foundCleaners);
      return foundCleaners;
    } catch (error) {
      throw new BadRequestException(
        `Failed to find unassigned cleaners of the working hours, try again.`
      );
    }
  }

  async findAllJobsByCleanerId(id: string, collectionDTO: CollectionDto) {
    try {
      const countJobs = await this.jobsModel.countDocuments({
        staffs: { $in: [id] },
        status: { $in: ["assigned", "process"] },
        ...collectionDTO.filter
      });
      const { nextOccurrences } = collectionDTO.filter || {};
      const { $gte, $lt } = nextOccurrences?.['$elemMatch'] || {};

      const baseQuery = {
        staffs: { $in: [id] },
        status: { $in: ["assigned", "process"] },
        ...collectionDTO.filter
      }

      const aggregationPipeline: PipelineStage[] = [
        {
          $match: baseQuery
        },
        {
          $skip: collectionDTO.page * collectionDTO.limit
        },
        {
          $limit: collectionDTO.limit
        },
        // Populate equivalent in aggregation
        {
          $lookup: {
            from: "bookingrequests",
            localField: "bookingRequest",
            foreignField: "_id",
            as: "bookingRequest"
          }
        },
        {
          $unwind: {
            path: "$bookingRequest",
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $addFields: {
            "address_id": {
              $toObjectId: "$bookingRequest.address"
            },
            "service_id": {
              $toObjectId: "$service"
            }
          }
        },
        {
          $lookup: {
            from: "addresses",
            localField: "address_id",
            foreignField: "_id",
            as: "bookingRequest.address"
          }
        },
        {
          $unwind: {
            path: "$bookingRequest.address",
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $lookup: {
            from: "bookingtypes",
            localField: "service_id",
            foreignField: "_id",
            as: "service"
          }
        },
        {
          $unwind: {
            path: "$service",
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $lookup: {
            from: "clienttypes",
            localField: "service.clientType",
            foreignField: "_id",
            as: "service.clientType"
          }
        },
        {
          $unwind: {
            path: "$service.clientType",
            preserveNullAndEmptyArrays: true
          }
        },
        // Populate addons
        {
          $lookup: {
            from: "bookingaddons",
            localField: "addons.addon",
            foreignField: "_id",
            as: "populatedAddons"
          }
        },
        {
          $addFields: {
            addons: {
              $map: {
                input: "$addons",
                as: "addon",
                in: {
                  quantity: "$$addon.quantity",
                  addon: {
                    $arrayElemAt: [
                      "$populatedAddons",
                      {
                        $indexOfArray: ["$populatedAddons._id", "$$addon.addon"]
                      }
                    ]
                  },
                  "_id": "$$addon._id"
                }
              }
            }
          }
        },
        // Populate extras
        {
          $lookup: {
            from: "bookingextras",
            localField: "extras.extra",
            foreignField: "_id",
            as: "populatedExtras"
          }
        },
        {
          $addFields: {
            extras: {
              $map: {
                input: "$extras",
                as: "extra",
                in: {
                  quantity: "$$extra.quantity",
                  extra: {
                    $arrayElemAt: [
                      "$populatedExtras",
                      {
                        $indexOfArray: ["$populatedExtras._id", "$$extra.extra"]
                      }
                    ]
                  },
                  "_id": "$$extra._id"
                }
              }
            }
          }
        },
        // Cleanup temporary fields
        {
          $project: {
            populatedAddons: 0,
            populatedExtras: 0,
            staffs: 0,
            frequency: 0,
            address_id: 0,
            service_id: 0
          }
        }
      ];

      if (collectionDTO.sort){
        aggregationPipeline.push({
          $sort: JSON.parse(collectionDTO.sort || "{}")
        });
      }

      if ($gte && $lt) {
        aggregationPipeline.push({
            $addFields: {
              matchedDate: {
                $filter: {
                  input: "$nextOccurrences",
                  as: "date",
                  cond: {
                    $and: [
                      { $gte: ["$$date", new Date($gte)] },
                      { $lt: ["$$date", new Date($lt)] }
                    ]
                  }
                }
              }
            }
          },
          {
            $sort: {
              "matchedDate": 1
            }
          });
      }

      const foundJobs = await this.jobsModel.aggregate(aggregationPipeline).exec();

      return {
        data: foundJobs,
        pagination: this.createPagination(countJobs, collectionDTO)
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to find all jobs by cleaner id, try again.`
      );
    }
  }

  async getJobsByWeek(startDate: string): Promise<any> {
    const parsedStartDate = dayjs(startDate).toDate();
    const parsedEndOfWeek = dayjs(startDate).endOf("week").toDate();
    const today = dayjs().tz("UTC").startOf("day").toDate();
    const endOfWeek = dayjs().tz("UTC").endOf("week").toDate();

    const baseQuery = {
      nextOccurrences: {
        $elemMatch: {
          $gte: startDate ? parsedStartDate : today,
          $lte: startDate ? parsedEndOfWeek : endOfWeek
        }
      }
    };

    const currentDay = dayjs(new Date(startDate)).toDate();

    const aggregationPipeline: PipelineStage[] = [
      {
        $match: baseQuery
      },
      {
        $lookup: {
          from: "bookingfrequencies",
          localField: "frequency",
          foreignField: "_id",
          as: "frequency",
        },
      },
      {
        $lookup: {
          from: "frequencydiscounts",
          localField: "frequency.discountFrequency",
          foreignField: "_id",
          as: "frequency.discountFrequency",
        },
      },
      {
        $lookup: {
          from: "bookingtypes",
          localField: "service",
          foreignField: "_id",
          as: "bookingTypes",
        },
      },
      {
        $lookup: {
          from: "bookingrequests",
          localField: "bookingRequest",
          foreignField: "_id",
          as: "request",
        },
      },
      {
        $addFields: {
          frequencyDate: {
            $first: "$frequency.frequency",
          },
        },
      },
      {
        $addFields: {
          hour: {
            $hour: {
              date: "$suitableDate", // replace with your date string field
              timezone: "UTC",
            },
          },
        },
      },
      {
        $addFields: {
          min: {
            $minute: {
              date: "$suitableDate", // replace with your date string field
              timezone: "UTC",
            },
          },
        },
      },
      {
        $addFields: {
          startUTC: {
            $dateFromString: {
              dateString: {
                $concat: [
                  { $dateToString: { date: currentDay, format: "%Y-%m-%d" } },
                  "T",
                  { $toString: "$hour" },
                  ":",
                  { $toString: "$min" },
                  ":00Z",
                ],
              },
            },
          },
        },
      },
      {
        $addFields: {
          start: {
            $toDate: {
              $dateToString: {
                date: "$startUTC",
                format: "%Y-%m-%dT%H:%M:%S.%LZ",
                // timezone: timezoneCode // replace with your timezone code
              },
            },
          },
        },
      },
      {
        $addFields: {
          endCheck: {
            $add: [
              "$start",
              { $multiply: [1000 * 60 * 60, { $divide: [{ $first: "$request.duration" }, 60] }] },
              { $multiply: [1000 * 60 * 60, { $divide: ["$hours", 60] }] }
            ]
          },
        },
      },
      {
        $addFields: {
          end: {
            $cond: {
              if: { $eq: ["$endCheck", null] }, // Check if 'end' field is null
              then: {
                $add: [
                  "$start",
                  { $multiply: [1000 * 60 * 60, { $divide: [{ $ifNull: [{ $first: "$request.duration" }, 120] }, 60] }] },
                  { $multiply: [1000 * 60 * 60, { $divide: [{ $ifNull: ["$hours", 0] }, 60] }] }
                ]
              },
              else: "$endCheck",
            },
          },
        },
      },
      {
        $addFields: {
          addressId: {
            $toObjectId: {
              $first: "$request.address",
            },
          },
        },
      },
      {
        $lookup: {
          from: "addresses", // replace with your actual addresses collection name
          localField: "addressId",
          foreignField: "_id",
          as: "addressContent",
        },
      },
      {
        $set: {
          _id: "$_id",
          title: {
            $concat: [
              { $first: "$request.firstName" },
              " ",
              { $first: "$request.lastName" },
            ],
          },
          address: {
            $concat: [
              { $first: "$addressContent.street_one" },
              ", ",
              { $first: "$addressContent.city" },
              ", ",
              { $first: "$addressContent.state" },
            ],
          },
          service: { $first: "$bookingTypes.name" },
          sentDate: "$dateCreated",
          status: "$status",
          frequency: { $first: "$frequency.title" },
          price: "$price",
          dateCreated: "$dateCreated",
        },
      },
      {
        $unset: [
          "bookingRequest",
          "request",
          "addons",
          "extras",
          "staffs",
          "video",
          "attachments",
          "__v",
          "bookingTypes",
          "notes",
          "startUTC",
          "hour",
          "endCheck",
          "clientTypeId",
          "addressId",
          "addressContent",
          "clientTypeContent",
          "sentDate",
          "price",
          // "workspace",
          // "suitableDate",
          "dateCreated",
        ],
      },
    ];

    const result = await this.jobsModel.aggregate(aggregationPipeline).exec();
    return result;
  }

  async countJobsOfCleanerByWeek(cleanerId: string, startDate: string) {
    try {
      const parsedStartDate = dayjs(startDate).startOf('day').toDate();
      const parsedEndOfWeek = dayjs(startDate).endOf("week").toDate();
      const today = dayjs().tz("UTC").startOf("day").toDate();
      const endOfWeek = dayjs().tz("UTC").endOf("week").toDate();

      console.log("parsedStartDate", parsedStartDate);
      console.log("parsedEndOfWeek", parsedEndOfWeek);

      return await this.jobsModel.aggregate([
        {
          $match: {
            staffs: { $in: [cleanerId] },
            suitableDate: {
              $gte: startDate ? parsedStartDate : today,
              $lte: startDate ? parsedEndOfWeek : endOfWeek
            }
          }
        },
        {
          $group: {
            _id: "$suitableDate",
            count: { $sum: 1 } // Count the number of jobs per day
          }
        },
        {
          $replaceWith: {
            date: "$_id",
            count: "$count"
          }
        }
      ]);
    } catch (error) {
      console.log("error", error);
      throw new BadRequestException(
        `Failed to find all jobs by cleaner id, try again.`
      );
    }
  }

  async startJob(id: string, userId: string) {
    const job = await this.jobsModel.findOne({
      _id: new mongoose.Types.ObjectId(id),
      staffs: { $in: [userId] }
    }).populate("staffs").exec();

    const staff = job.staffs.find(staff => staff._id.toString() === userId);

    if (!job) {
      throw new BadRequestException(`Job not found`);
    }
    const existingStaffStatus = job.staffStatus.find(stt => stt.staff.equals(userId));

    if (job.status === "process" && existingStaffStatus?.status === "process") {
      return job;
    }
    if (existingStaffStatus?.status === "completed") {
      throw new BadRequestException(`Staff has completed the job`);
    }
    if (job.status === "completed") {
      throw new BadRequestException(`Job is already completed`);
    }
    if (job.status === "unassigned") {
      throw new BadRequestException(`Job is not assigned`);
    }


    if (existingStaffStatus) {
      existingStaffStatus.status = "process";
    } else {
      job.staffStatus.push({
        staff: new mongoose.Types.ObjectId(userId),
        updatedAt: dayjs.utc().toDate(),
        status: "process"
      });
    }

    const notiMsg = getNotiMessage("");
    await this.notificationService.sendAdminNotification(job.workspace._id.toString(), {
      title: notiMsg["other-start-job"](staff.firstName).title,
      body: notiMsg["other-start-job"](staff.firstName).body,
      data: {
        screen: PortalScreens.Job,
        id: job._id.toString()
      },
    });

    job.status = "process";
    return job.save();
  }

  async completeJob(id: string, userId: string, medias: { mediaId: string[]; beforeAfter: string }[]) {
    const job = await this.jobsModel.findOne({
      _id: new mongoose.Types.ObjectId(id),
      staffs: { $in: [userId] }
    }).populate({
      path: 'frequency',
      populate: 'discountFrequency'
    }).exec();

    if (!job) {
      throw new BadRequestException(`Job not found`);
    }
    if (job.status === "completed") {
      throw new BadRequestException(`Job is already completed`);
    }
    if (job.status !== "process") {
      throw new BadRequestException(`Job is not in process`);
    }

    const existingStaffStatus = job.staffStatus.find(stt => stt.staff.equals(userId));

    const beforeMedias = medias.filter(media => media.beforeAfter === BeforeAfterEnum.BEFORE);
    const afterMedias = medias.filter(media => media.beforeAfter === BeforeAfterEnum.AFTER);

    const beforeMediasData = await this.mediasModel.find({
      mediaId: { $in: beforeMedias.flatMap(media => media.mediaId) }
    }).exec();
    const afterMediasData = await this.mediasModel.find({
      mediaId: { $in: afterMedias.flatMap(media => media.mediaId) }
    }).exec();

    const beforeMediasBodyData = {
      staff: new mongoose.Types.ObjectId(userId),
      medias: beforeMediasData.map(media => media._id),
      beforeAfter: BeforeAfterEnum.BEFORE,
      updatedAt: dayjs.utc().toDate()
    };

    const afterMediasBodyData = {
      staff: new mongoose.Types.ObjectId(userId),
      medias: afterMediasData.map(media => media._id),
      beforeAfter: BeforeAfterEnum.AFTER,
      updatedAt: dayjs.utc().toDate()
    };


    if (!existingStaffStatus) {
      throw new BadRequestException(`Staff has not started the job`);
    }
    existingStaffStatus.status = "completed";

    const notiMsg = getNotiMessage("");
    const staffList = job.staffs.map(staff => staff.toString());
    if (job.staffStatus.every(stt => stt.status === "completed")) {
      job.status = "completed";

      // Send notification to client
      await this.notificationService.sendMulticastNotification(staffList, {
        title: notiMsg["completed-job"].title,
        body: notiMsg["completed-job"].body,
        data: {
          screen: MobileAppScreens.Home
        }
      });
      await this.notificationService.sendAdminNotification(job.workspace._id.toString(), {
        title: notiMsg["completed-job"].title,
        body: notiMsg["completed-job"].body,
        data: {
          screen: PortalScreens.Job,
          id: job._id.toString()
        },
      });
      await this.updateNextOccurrences(job);
    } else {
      const completedStaff = await this.usersModel.findById(userId).exec();
      await this.notificationService.sendMulticastNotification(staffList.filter(staffId => staffId !== userId), {
        title: notiMsg["other-complete-job"](completedStaff.firstName).title,
        body: notiMsg["other-complete-job"](completedStaff.firstName).body,
        data: {
          screen: MobileAppScreens.Home
        }
      });
      await this.notificationService.sendAdminNotification(job.workspace._id.toString(), {
        title: notiMsg["other-complete-job"](completedStaff.firstName).title,
        body: notiMsg["other-complete-job"](completedStaff.firstName).body,
        data: {
          screen: PortalScreens.Job,
          id: job._id.toString()
        },
      });
    }

    await this.invoiceSchedulerService.processOnCompleteInvoices(job._id.toString())

    job.staffMedias.push(beforeMediasBodyData);
    job.staffMedias.push(afterMediasBodyData);

    job.dateServiced = dayjs().utc().startOf('day').toDate();

    return job.save();

  }

  async getCleanerStatusOfJob(jobId: string) {
    return await this.jobsModel.findOne({
      _id: new mongoose.Types.ObjectId(jobId)
    }, {
      staffStatus: 1
    }).populate("staffStatus.staff").exec();
  }

  async downloadCleanerReport(jobId: string, workspace: string) {
    const job = await this.findOne(jobId, workspace);
    if (!job) {
      throw new BadRequestException(`Job not found`);
    }
    if (job.status !== "completed") {
      throw new BadRequestException(`Job is not completed`);
    }
    const clientName = `${job.bookingRequest.firstName} ${job.bookingRequest.lastName}`;
    const bookingAddress = job.bookingRequest.address as unknown as Address;

    const groupedStaffMedias = job.staffMedias.reduce((acc,staffMedia) => {
      if (!acc[staffMedia.staff.toString()]) {
        acc[staffMedia.staff.toString()] = { before: [], after: [] };
      }
      acc[staffMedia.staff.toString()][staffMedia.beforeAfter].push(...staffMedia.medias);
      return acc;
    }, {});
    const transformedStaffMedias = Object.entries(groupedStaffMedias as Record<string, { before: MediasDocument[], after: MediasDocument[] }>).map(([staffId, medias]) => {
      const staff = job.staffs.find((staff) => staff._id.toString() === staffId);

      if (!staff) {
        return undefined;
      }

      const domain = `${process.env.API_ROOT_URL}/medias/`
      const beforeImageBuffer = medias.before.map((media) => {
        return domain + media.mediaId + `?workspaceId=${workspace}`;
      });
      const afterImageBuffer = medias.after.map((media) => {
        return domain + media.mediaId + `?workspaceId=${workspace}`;
      });

      return {
        staffName: staff.firstName + ' ' + staff.lastName,
        before: beforeImageBuffer,
        after: afterImageBuffer
      }
    })

    const public_url = `${process.env.SPACES_URL}/${process.env.SPACES_FOLDER_NAME}/`
    const pdf = await this.generatePdfService.generateCleanerReportPdf({
      clientName,
      street_one: bookingAddress.street_one,
      city: bookingAddress.city,
      state: bookingAddress.state,
      dateServiced: dayjs(job.dateServiced).format("MMM DD, YYYY"),
      logo: public_url + job.workspace.logo,
      staffMedias: transformedStaffMedias
    });

    const cleanerReportId = job._id.toString();

    const media = await this.mediasService.upload('Cleaner-Report-' + cleanerReportId + '.pdf', pdf,'application/pdf');

    return media.mediaId;
  }

  async triggerUpdateNextOccurrences(jobId: string) {
    const job = await this.jobsModel.findById(jobId);
    if (!job) {
      throw new Error('Job not found');
    }
    const freq = await this.bookingFrequencyService.findOne(job.frequency.toString(), job.workspace.toString());
    if (!freq) {
      throw new Error('Frequency not found');
    }
    job.rrule = this.createRRule(freq, job.suitableDate, job.endDate);
    const jobOccur = await this.updateNextOccurrences(job);
    await this.updateChildJobsOccurrence(jobOccur);

    return jobOccur;
  }

  private shouldUpdateCache(job: JobsDocument): boolean {
    const now = dayjs().utc().startOf('day').toDate();
    const timeSinceLastCalc = now.getTime() - job.lastCalculated.getTime();
    const daysSinceLastCalc = timeSinceLastCalc / (1000 * 60 * 60 * 24); // 1 day in milliseconds

    return (
      daysSinceLastCalc > (this.CACHE_DURATION_DAYS / 2) ||
      job.nextOccurrences.length < 3 ||
      job.nextOccurrences[0] < now
    );
  }

  /**
   * Normalize a date to the start of day (00:00:00.000)
   * to avoid time comparison issues
   */
  private normalizeDate(date: Date | string | number): Date {
    try {
      const normalized = new Date(date);
      normalized.setHours(0, 0, 0, 0);
      return normalized;
    } catch (error) {
      console.error('Error normalizing date:', error);
      return new Date(0); // Return epoch time as fallback
    }
  }

  private isExcluded(date: Date, excludedDates: Date[]): boolean {
    if (!date || !Array.isArray(excludedDates)) {
      return false;
    }
    
    // Normalize input date to start of day for comparison
    const normalizedDate = this.normalizeDate(date);
    
    return excludedDates.some(excludedDate => {
      if (!excludedDate) {
        return false;
      }
      
      try {
        // Convert to Date object if it's not already and normalize
        const normalizedExcludedDate = this.normalizeDate(excludedDate);
        
        // Compare timestamps for equality
        return normalizedExcludedDate.getTime() === normalizedDate.getTime();
      } catch (error) {
        console.error('Error comparing dates in isExcluded:', error);
        return false;
      }
    });
  }

  async updateNextOccurrences(job: JobsDocument): Promise<Jobs> {
    if (!job.save) {
      job = await this.jobsModel.findById(job._id);
    }

    const now = dayjs().tz(job.timezone).startOf('date').toDate();
    const futureDate = dayjs().tz(job.timezone).startOf('date').toDate();
    futureDate.setDate(now.getDate() + this.CACHE_DURATION_DAYS);
  
    if (!job.rrule) {
      if (!job.nextOccurrences.includes(job.suitableDate)) {
        job.nextOccurrences.push(job.suitableDate);
      }
      return await job.save();
    }
    // Parse the rule with option to keep the original timezone intent
    const options = RRule.parseString(job.rrule);
    const rule = new RRule(options);

    const occurrences = rule.between(now, futureDate, true)
      .filter(date => !this.isExcluded(date, job.excludedDates))
      .slice(0, this.MAX_OCCURRENCES);

    // Get the timezone of the machine
    const machineTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    // Since rule.between is timezone sensitive, we need to convert the dates to UTC
    const utcOccurrences = occurrences.map(date => {
      const dateWithoutZulu = date.toISOString().replace('Z', '');
      // Actually use machine timezone
      const tzDate = dayjs.tz(dateWithoutZulu,machineTimezone).utc();
      return datetime(tzDate.year(), tzDate.month() + 1, tzDate.date(), tzDate.hour(), tzDate.minute(), tzDate.second());
    });
  
    const newOccurrences = Array.from(new Set([...job.nextOccurrences, ...utcOccurrences]))
      // Sort occurrences chronologically
      .sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
  
    if (JSON.stringify(job.nextOccurrences) === JSON.stringify(newOccurrences)) {
      return job;
    }
  
    job.nextOccurrences = newOccurrences;
    job.lastCalculated = now;
  
    return await job.save();
  }

  async getHistoricalJobs(id: string, workspace: string): Promise<Jobs[]> {
    // Check if job is parent job
    const job = await this.jobsModel.findById(id);
    if (!job) {
      throw new HttpException("Job is not existed", HttpStatus.NOT_FOUND);
    }
    const parentJobId = job.parent ? job.parent : job._id;

    const jobs = await this.jobsModel.find({
      $or: [
        { _id: parentJobId },
        { parent: parentJobId }
      ],
      workspace: new mongoose.Types.ObjectId(workspace),
      status: {
        $in: ["process", "completed"]
      },
    }, {
      _id: 1,
      suitableDate: 1
    }).limit(50).exec();
    return jobs;
  }

  private createRRule(frequency: BookingFrequency, suitableDate: Date, endDate?: Date, tz?: string): string {
    const tzSuitableDate = dayjs(suitableDate).tz(tz);
    const dtSuitableDate = datetime(tzSuitableDate.year(), tzSuitableDate.month() + 1, tzSuitableDate.date(), tzSuitableDate.hour(), tzSuitableDate.minute(), tzSuitableDate.second());
    const dtEndDate = endDate ? datetime(dayjs(endDate).year(), dayjs(endDate).month() + 1, dayjs(endDate).date(), 0, 0, 0) : undefined;

    const baseOptions: Partial<Options> = {
      dtstart: dtSuitableDate,
      until: dtEndDate,
      interval: frequency.interval,
    };

    switch (frequency.type) {
      case 'once':
        return '';
      case 'daily':
        return this.createDailyRule(frequency, baseOptions, tz);
      case 'weekly':
        return this.createWeeklyRule(frequency, baseOptions, tz);
      case 'monthly':
        return this.createMonthlyRule(frequency, baseOptions, tz);
      case 'yearly':
        return this.createYearlyRule(frequency, baseOptions, tz);
    }
  }

  private createDailyRule(
    frequency: BookingFrequency,
    baseOptions: Partial<Options>,
    tz?: string,
  ): string {
    const options: Partial<Options> = {
      ...baseOptions,
      freq: RRule.DAILY,
    };

    if (tz) {
      options.tzid = tz;
    }

    if (frequency.onlyWeekdays) {
      options.byweekday = [
        RRule.MO,
        RRule.TU,
        RRule.WE,
        RRule.TH,
        RRule.FR
      ];
    }

    return new RRule(options).toString();
  }

  private createWeeklyRule(
    frequency: BookingFrequency,
    baseOptions: Partial<Options>,
    tz?: string,
  ): string {
    if (tz) {
      baseOptions.tzid = tz;
    }
    return new RRule({
      ...baseOptions,
      freq: RRule.WEEKLY,
      byweekday: frequency.daysOfWeek.map(day => this.weekdayMap[day]),
    }).toString();
  }

  private createMonthlyRule(
    frequency: BookingFrequency,
    baseOptions: Partial<Options>,
    tz?: string,
  ): string {
    const options: Partial<Options> = {
      ...baseOptions,
      freq: RRule.MONTHLY,
    };

    if (tz) {
      options.tzid = tz;
    }

    if (frequency.dayOfMonth) {
      options.bymonthday = frequency.dayOfMonth;
    } else if (frequency.weekday) {
      options.byweekday = this.weekdayMap[frequency.weekday.day]
        .nth(frequency.weekday.week);
    }

    return new RRule(options).toString();
  }

  private createYearlyRule(
    frequency: BookingFrequency,
    baseOptions: Partial<Options>,
    tz?: string,
  ): string {
    const options: Partial<Options> = {
      ...baseOptions,
      freq: RRule.YEARLY,
      bymonth: frequency.month,
    };

    if (tz) {
      options.tzid = tz;
    }

    if (frequency.dayOfMonth) {
      options.bymonthday = frequency.dayOfMonth;
    } else if (frequency.weekday) {
      options.byweekday = this.weekdayMap[frequency.weekday.day]
        .nth(frequency.weekday.week);
    }

    return new RRule(options).toString();
  }

  private async createChildJobs(parentJob: Jobs): Promise<Jobs[]> {
    const now = dayjs().utc().startOf('day').toDate();

    // @ts-ignore
    parentJob = parentJob.toObject();

    const existingChildJobs = await this.jobsModel.find({ parent: parentJob._id }).exec();

    const occurrences = parentJob.nextOccurrences;

    const filteredOccurrences = occurrences.filter(occurrence => {
      return !existingChildJobs.some(job => job.suitableDate.getTime() === occurrence.getTime());
    }).filter(date => parentJob.suitableDate.getTime() !== date.getTime());

    const childJobs = filteredOccurrences.map(date => ({
      ...parentJob,
      _id: new mongoose.Types.ObjectId(),
      suitableDate: date,
      nextOccurrences: [],
      parent: parentJob._id,
      staffs: parentJob.staffs,
      status: parentJob.status,
      // Reset status and staffs
      staffStatus: [],
      staffMedias: [],
      dateCreated: now,
      dateServiced: null,
    }));

    // @ts-ignore
    return await this.jobsModel.insertMany(childJobs);
  }

  private async updateChildJobs(parentJob: Jobs) {
    return this.jobsModel.updateMany({ parent: parentJob._id }, {
      $set: {
        bookingRequest: parentJob.bookingRequest,
        service: parentJob.service,
        addons: parentJob.addons,
        extras: parentJob.extras,
        workspace: parentJob.workspace,
        frequency: parentJob.frequency,
        price: parentJob.price,
        hours: parentJob.hours,
        discountCode: parentJob.discountCode,
        rrule: parentJob.rrule,
        duration: parentJob.duration,
        nextOccurrences: [],
      }
    }, { new: true });
  }

  private async updateChildJobsOccurrence(parentJob: Jobs) {
    const occurrences = parentJob.nextOccurrences;
    const childJobs = await this.jobsModel.find({
      parent: parentJob._id, status: {
        $in: ["unassigned", "assigned"]
      }
    }).exec();

    // Delete child jobs that are not in the next occurrences
    const deletedChildJobs = childJobs.filter(job => !occurrences.includes(job.suitableDate));
    await this.jobsModel.deleteMany({ _id: { $in: deletedChildJobs.map(job => job._id) } }).exec();

    // Create new child jobs
    await this.createChildJobs(parentJob);
  }

  genJobNumber(job: Jobs, newId?: mongoose.Types.ObjectId) {
    let jobName = "";
    const id = (job._id || newId)?.toString() || "";
    if (!id) return "";
    const jobId = id.toString().slice(-4).toLocaleUpperCase();

    return `${jobName}${job._id.toString().slice(-6).toLocaleUpperCase()}`
  }

  private adjustDateForTimezone(date: Date | string, timezone: string = 'UTC', isStartOfDay: boolean = true): Date {
    if (!date) return null;
    
    const dateObj = dayjs(date).tz(timezone);
    const utcDate = isStartOfDay ? 
      dateObj.startOf('day').utc().toDate() : 
      dateObj.endOf('day').utc().toDate();
    
    return utcDate;
  }
  // Helper function to calculate job status counts
  private calculateJobStatusCounts(jobs: any[]): any[] {
    const counts = {};
    jobs.forEach(job => {
      if (!counts[job.status]) {
        counts[job.status] = 0;
      }
      counts[job.status]++;
    });

    return Object.entries(counts).map(([status, count]) => ({
      status,
      count
    }));
  }

  // Helper function to calculate overall job status counts
  private calculateOverallJobStatusCounts(jobs: any[]): any[] {
    return this.calculateJobStatusCounts(jobs);
  }

  /**
   * Find all jobs for external API integration with JobON
   * This method provides a simplified response structure for external systems
   */
  async findAllExternal(collectionDto: CollectionDto): Promise<any> {
      const jobs = await this.jobsModel
          .find(collectionDto.filter)
          .skip(collectionDto.page * collectionDto.limit)
          .limit(collectionDto.limit)
          .populate({
              path: 'bookingRequest',
              populate: { path: 'address' },
          })
          .populate('service')
          .populate('frequency')
          .populate('staffs', '_id firstName lastName email phone')
          .sort(JSON.parse(collectionDto.sort || '{}'))
          .lean()
          .exec();

      // Transform data for external API consumption
      const transformedJobs = jobs.map((job) => ({
          id: job._id,
          status: job.status,
          price: job.price,
          hours: job.hours,
          dateCreated: job.dateCreated,
          dateServiced: job.dateServiced,
          suitableDate: job.suitableDate,
          notes: job.notes,
          service: job.service
              ? {
                    id: job.service._id,
                    name: job.service.name,
                    description: job.service.name, // Using name as description since description doesn't exist
                }
              : null,
          frequency: job.frequency
              ? {
                    id: job.frequency._id,
                    title: job.frequency.title,
                }
              : null,
          location: job.bookingRequest?.address
              ? {
                    address:
                        typeof job.bookingRequest.address === 'string'
                            ? job.bookingRequest.address
                            : (job.bookingRequest.address as any)?.address ||
                              '',
                    city:
                        typeof job.bookingRequest.address === 'string'
                            ? ''
                            : (job.bookingRequest.address as any)?.city || '',
                    state:
                        typeof job.bookingRequest.address === 'string'
                            ? ''
                            : (job.bookingRequest.address as any)?.state ||
                              '',
                    zipCode:
                        typeof job.bookingRequest.address === 'string'
                            ? ''
                            : (job.bookingRequest.address as any)?.zipCode ||
                              '',
                    country:
                        typeof job.bookingRequest.address === 'string'
                            ? ''
                            : (job.bookingRequest.address as any)?.country ||
                              '',
                    lat:
                        typeof job.bookingRequest.address === 'string'
                            ? null
                            : (job.bookingRequest.address as any)?.lat ||
                              null,
                    lng:
                        typeof job.bookingRequest.address === 'string'
                            ? null
                            : (job.bookingRequest.address as any)?.lng ||
                              null,
                }
              : null,
          client: job.bookingRequest
              ? {
                    name: `${job.bookingRequest.firstName || ''} ${
                        job.bookingRequest.lastName || ''
                    }`.trim(),
                    email: job.bookingRequest.email,
                    phone: job.bookingRequest.phone,
                }
              : null,
          staffs: job.staffs
              ? job.staffs.map((staff) => ({
                    id: staff._id,
                    name: `${staff.firstName || ''} ${
                        staff.lastName || ''
                    }`.trim(),
                    email: staff.email,
                    phone: staff.phoneNumber || '', // Using phoneNumber as fallback
                }))
              : [],
      }));

      return {
          data: transformedJobs,
          pagination: await this.paginate(collectionDto),
      };
  }

  /**
   * Collect all data needed for webhook notification
   */
  async collectWebhookData(jobId: string): Promise<{ job: Jobs; bookingRequest: BookingRequest; address: Address } | null> {
    try {
      // Find the job with populated references
      const job = await this.jobsModel
        .findById(jobId)
        .populate({
          path: 'bookingRequest',
          populate: {
            path: 'address'
          }
        })
        .exec();

      if (!job) {
        this.logger.warn(`Job not found for webhook data collection: ${jobId}`);
        return null;
      }

      const bookingRequest = job.bookingRequest as any;
      const address = bookingRequest?.address as any;

      if (!bookingRequest || !address) {
        this.logger.warn(`Missing booking request or address data for job: ${jobId}`);
        return null;
      }

      return {
        job,
        bookingRequest,
        address
      };
    } catch (error) {
      this.logger.error(`Error collecting webhook data for job ${jobId}:`, error.message);
      return null;
    }
  }
}
