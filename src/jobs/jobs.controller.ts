import {
  Body,
  Controller,
  Get,
  Delete,
  HttpCode,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query,
  Patch,
  BadRequestException,
  UseInterceptors,
  UseGuards,
  Req
} from "@nestjs/common";
import { JobsService } from "./jobs.service";
import { JobNotificationService } from "../job-notification/job-notification.service";
import {
  CollectionDto,
  CollectionResponse
} from "@forlagshuset/nestjs-mongoose-paginate";
import CreateJobDto from "./Dto/CreateJobDto";
import UpdateJobDto from "./Dto/UpdateJobDto";
import { Jobs } from "./jobs.schema";
import mongoose from "mongoose";
import { JobsInterceptor } from "./jobs.interceptor";
import { AuthGuard, UserRequest } from '../auth/auth.guard';
import { ACGuard, UseRoles } from "nest-access-control";
import { Public } from '../@core/decorators/public.decorator';
import ROLE_RESOURCES from "../@core/constants/roles.resources";
import { ApiTags, ApiBody } from "@nestjs/swagger";
import { Request } from "express";
import * as jwt from "jsonwebtoken";
import { UsersService } from "src/users/users.service";
import { RolesService } from "src/roles/roles.service";
import { InvoicesService } from "src/invoices/invoices.service";
import { UpdateInvoiceSchedulingDto } from "../invoices/Dto/UpdateInvoiceSchedulingDto";
import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";

// Configure dayjs plugins
dayjs.extend(utc);
dayjs.extend(timezone);

@Controller(ROLE_RESOURCES.JOBS)
@ApiTags("Jobs")
export class JobsController {
  constructor(
    private jobsService: JobsService,
    private usersService: UsersService,
    private rolesService: RolesService,
    private invoicesService: InvoicesService,
    private jobNotificationService: JobNotificationService,
  ) {
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get("/")
  @UseInterceptors(JobsInterceptor)
  async getJobs(
    @Query()
      collectionDto: CollectionDto,
    @Body("workspace") workspace: string
  ): Promise<CollectionResponse<Jobs>> {
    if (!collectionDto?.filter) {
      collectionDto = { ...collectionDto, filter: {} };
    }

    collectionDto.filter["workspace"] = {
      $eq: new mongoose.Types.ObjectId(workspace)
    };
    return this.jobsService.findAll(collectionDto);
  }

  // External API for JobON integration
  @Public()
  @HttpCode(HttpStatus.OK)
  @Get("/external/all")
  async getJobsExternal(
    @Query("workspace") workspace?: string,
    @Query("page") page?: string,
    @Query("limit") limit?: string
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    
    try {
      const collectionDto: CollectionDto = {
        page: pageNum,
        limit: limitNum,
        filter: {},
        sort: JSON.stringify({ dateCreated: -1 })
      };

      // Add workspace filter if provided
      if (workspace) {
        collectionDto.filter["workspace"] = {
          $eq: new mongoose.Types.ObjectId(workspace)
        };
      }

      const result = await this.jobsService.findAllExternal(collectionDto);
      
      return {
        statusCode: HttpStatus.OK,
        message: "Get jobs success",
        data: result.data,
        pagination: result.pagination
      };
    } catch (error) {
      throw new HttpException(
        "Failed to fetch jobs",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get("/clients/:id")
  async getJobsByClientId(
    @Query()
      collectionDto: CollectionDto,
    @Param("id") id: string,
    @Body("workspace") workspace: string
  ): Promise<CollectionResponse<Jobs>> {
    return this.jobsService.findAllByClientId(id, workspace, collectionDto);
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get("/rrule")
  async getJobsComplexByRruleString(
    @Query()
      collectionDto: CollectionDto,
    @Body("workspace") workspace: string
  ) {
    if (!collectionDto?.filter) {
      collectionDto = { ...collectionDto, filter: {} };
    }
    
    if (!collectionDto.filter?.timezone) {
      collectionDto.filter.timezone = "UTC";
    }
    
    try {
      dayjs().tz(collectionDto.filter.timezone as string);
    } catch (error) {
      throw new BadRequestException(`Invalid timezone: ${collectionDto.filter.timezone}`);
    }
    
    collectionDto.filter["workspace"] = {
      $eq: new mongoose.Types.ObjectId(workspace)
    };
    let jobs = await this.jobsService.complexByRruleStringCalc(
      collectionDto
    );
    if (!jobs) {
      throw new HttpException(
        "Jobs is not existed",
        HttpStatus.BAD_REQUEST
      );
    }
    
    // Check if pagination data exists and include it in the response
    if (jobs.pagination) {
      return {
        statusCode: HttpStatus.OK,
        data: jobs.data,
        jobStatus: jobs.jobStatus,
        pagination: jobs.pagination,
        message: "Get jobs success"
      };
    }
    
    return {
      statusCode: HttpStatus.OK,
      data: jobs.data,
      jobStatus: jobs.jobStatus,
      message: "Get jobs success"
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get("/rrule/calendar")
  async getJobsComplexByRruleStringCal(
    @Req() req: UserRequest,
    @Query()
      collectionDto: CollectionDto,
  ) {
    const workspace = req.user.workspace;
    
    let mutableDto: any = { ...collectionDto };
    
    if (!mutableDto?.filter) {
      mutableDto.filter = {};
    }
    
    if (!mutableDto.filter?.timezone) {
      mutableDto.filter.timezone = "UTC";
    }
    
    try {
      dayjs().tz(mutableDto.filter.timezone as string);
    } catch (error) {
      throw new BadRequestException(`Invalid timezone: ${mutableDto.filter.timezone}`);
    }

    const page = mutableDto.filter?.page || '1';
    const limit = mutableDto.filter?.limit || '20';

    delete mutableDto.filter.page;
    delete mutableDto.filter.limit;
    
    const hasSearchFilter = typeof mutableDto.filter?.search === 'string' && mutableDto.filter.search.trim() !== '';
    const hasStatusFilter = typeof mutableDto.filter?.status === 'string' && mutableDto.filter.status.trim() !== '';
    
    if (hasSearchFilter || hasStatusFilter) {
      mutableDto.limit = parseInt(limit);
      mutableDto.page = parseInt(page) - 1;
    } else {
      mutableDto.limit = undefined;
      mutableDto.page = undefined;
    }
    
    mutableDto.filter["workspace"] = {
      $eq: new mongoose.Types.ObjectId(workspace)
    };
    
    let jobs = await this.jobsService.complexByRruleString(
      mutableDto
    );
    
    if (!jobs) {
      throw new HttpException(
        "Jobs is not existed",
        HttpStatus.BAD_REQUEST
      );
    }
    
    // Check if pagination data exists and include it in the response
    if (jobs.pagination) {
      return {
        statusCode: HttpStatus.OK,
        data: jobs.data,
        jobStatus: jobs.jobStatus,
        pagination: jobs.pagination,
        message: "Get jobs success"
      };
    }
    
    return {
      statusCode: HttpStatus.OK,
      data: jobs.data,
      jobStatus: jobs.jobStatus,
      message: "Get jobs success"
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get("weekly")
  async getWeeklyJobs(
    @Query("startDate") startDate: string,
  ) {
    const jobs = await this.jobsService.getJobsByWeek(startDate);
    return {
      statusCode: HttpStatus.OK,
      message:
        "Find all jobs by week successfully",
      data: jobs
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @Get("booking-request/:id")
  async getJobsByBookingRequestId(
    @Param("id") brId: string,
    @Req() req: UserRequest
  ) {
    const workspace = req.user.workspace || "";
    const jobs = await this.jobsService.findByBookingRequestId(brId, workspace.toString());
    return {
      statusCode: HttpStatus.OK,
      message:
        "Find job by quote id successfully",
      data: jobs
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get(":id")
  async getJob(
    @Param("id") id: string,
    @Req() req: UserRequest
  ) {
    let workspace = req.user.workspace || '';
    let job = await this.jobsService.findOne(id, workspace.toString());
    if (!job) {
      throw new HttpException(
        "Job is not existed",
        HttpStatus.BAD_REQUEST
      );
    }
    return {
      statusCode: HttpStatus.OK,
      message: "Get job success",
      data: job
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "create",
    possession: "any"
  })
  @Post("/")
  async createJob(@Body() job: CreateJobDto) {
    const existingJob = await this.jobsService.findDuplicateJob(job);
  
    if (existingJob) {
      return {
        statusCode: HttpStatus.OK,
        message: "Job already exists",
        data: existingJob
      };
    }

    // Create new job if no duplicate found
    let itemCreated = await this.jobsService.create(job);
    if (!itemCreated) {
      throw new HttpException(
        "Can not create Job",
        HttpStatus.BAD_REQUEST
      );
    }

    // Send webhook notification asynchronously (don't block job creation)
    this.sendJobNotificationAsync(itemCreated._id.toString());

    return {
      statusCode: HttpStatus.OK,
      message: "Create job successfully",
      data: itemCreated
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "update",
    possession: "any"
  })
  @Patch("/:id")
  async updateJob(
    @Param("id") id: string,
    @Body() job: UpdateJobDto,
    @Req() request: Request
  ) {
    try {
      const [type, token] =
      request.headers.authorization?.split(" ") ?? [];
      // type === "Bearer" ? token : undefined;
      const payload = jwt.decode(type);
      const user = await this.usersService.findOne(
        payload["sub"].toString(),
        job.workspace
      );
      const role = await this.rolesService.getRole(user?.roles[0]);
      let userId =
        role?.name === "Cleaner"
          ? payload["sub"].toString()
          : undefined;
      let itemUpdated = await this.jobsService.update(id, job, userId);
      if (!itemUpdated) {
        throw new HttpException(
          "Can not update Job",
          HttpStatus.BAD_REQUEST
        );
      }

      return {
        statusCode: HttpStatus.OK,
        message: "Update job successfully",
        data: itemUpdated
      };
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "delete",
    possession: "any"
  })
  @Delete(":id")
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        workspace: { type: 'string', example: '507f1f77bcf86cd799439011', description: 'Workspace ID' }
      },
      required: ['workspace']
    }
  })
  async deleteJob(
    @Param("id") id: string,
    @Body("workspace") workspace: string,
    @Query("occurrenceDate") occurrenceDate?: string,
    @Query("deleteEntireSeries") deleteEntireSeries?: boolean
  ) {
    try {
      // Validate input parameters
      if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(workspace)) {
        throw new HttpException(
          "Invalid ID format",
          HttpStatus.BAD_REQUEST
        );
      }
      
      if (occurrenceDate && isNaN(new Date(occurrenceDate).getTime())) {
        throw new HttpException(
          "Invalid occurrence date format",
          HttpStatus.BAD_REQUEST
        );
      }
      
      // Get job to determine its type
      const job = await this.jobsService.findOne(id, workspace);
      
      if (!job) {
        throw new HttpException("Job not found", HttpStatus.NOT_FOUND);
      }
      
      const isChild = job.parent !== undefined && job.parent !== null;
      const isRecurring = job.rrule && job.rrule.length > 0;
      
      // Case 1: Delete entire series (explicitly requested or one-time job)
      if (deleteEntireSeries === true || (!isRecurring && !isChild)) {
        const itemDeleted = await this.jobsService.delete(id, workspace);
        return {
          statusCode: HttpStatus.OK,
          message: "Delete job (and all occurrences) successfully",
          data: itemDeleted
        };
      } 
      
      // Case 2: Delete specific occurrence of a recurring job
      else if (isRecurring && !isChild) {
        const dateToDelete = occurrenceDate ? new Date(occurrenceDate) : job.suitableDate;
        await this.jobsService.cancelOccurrence(id, dateToDelete);
        
        return {
          statusCode: HttpStatus.OK,
          message: "Deleted job occurrence successfully",
        };
      }
      
      // Case 3: Delete a child job
      else if (isChild) {
        const itemDeleted = await this.jobsService.delete(id, workspace);
        
        return {
          statusCode: HttpStatus.OK,
          message: "Deleted job occurrence successfully",
          data: itemDeleted
        };
      }
      
      // Fallback
      else {
        const itemDeleted = await this.jobsService.delete(id, workspace);
        return {
          statusCode: HttpStatus.OK,
          message: "Delete job successfully",
          data: itemDeleted
        };
      }
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        error.message || "An error occurred while deleting the job",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get("/nearest-cleaners/:clientEmail")
  async findNearestToFurthestCleaners(
    @Param("clientEmail") clientEmail: string
  ) {
    const foundCleaners =
      await this.jobsService.findNearestToFarthestCleaners(clientEmail);
    if (foundCleaners.length <= 0) {
      throw new HttpException(
        "Not found any nearest cleaner",
        HttpStatus.BAD_REQUEST
      );
    }

    return {
      statusCode: HttpStatus.OK,
      message: "Find cleaners successfully",
      data: foundCleaners
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get("/assigned-cleaners/:id")
  async findAssignedCleaners(@Param("id") id: string) {
    const foundCleaners = await this.jobsService.findAssignedCleaners(id);
    if (foundCleaners.length <= 0) {
      throw new HttpException(
        "Not found any assigned cleaner",
        HttpStatus.BAD_REQUEST
      );
    }

    return {
      statusCode: HttpStatus.OK,
      message: "Find assigned cleaners successfully",
      data: foundCleaners
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get("/unassigned-cleaners/:id")
  async findOutOfJobWorkingHoursCleaner(@Param("id") id: string) {
    const foundCleaners =
      await this.jobsService.findOutOfJobWorkingHoursCleaner(id);
    if (foundCleaners.length <= 0) {
      throw new HttpException(
        "Not found any unassigned cleaners of the working hours",
        HttpStatus.BAD_REQUEST
      );
    }

    return {
      statusCode: HttpStatus.OK,
      message:
        "Find unassigned cleaners of the working hours successfully",
      data: foundCleaners
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get("/cleaners/:id")
  async getJobsByCleanerId(
    @Query()
      collectionDto: CollectionDto,
    @Param("id") id: string
  ) {
    const jobs = await this.jobsService.findAllJobsByCleanerId(id, collectionDto);
    return {
      statusCode: HttpStatus.OK,
      message:
        "Find all jobs of the cleaner successfully",
      data: jobs
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get("/cleaners/weekly/:id")
  async getJobsByWeek(
    @Query("startDate") startDate: string,
    @Param("id") id: string
  ) {
    const jobCount = await this.jobsService.countJobsOfCleanerByWeek(id, startDate);
    return {
      statusCode: HttpStatus.OK,
      message:
        "Find all jobs of the cleaner by week successfully",
      data: jobCount
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Post("/start/:id")
  async startJob(
    @Param("id") id: string,
    @Body("userId") userId: string
  ) {
    if (!userId) {
      throw new HttpException(
        "User id is required",
        HttpStatus.BAD_REQUEST
      );
    }

    await this.jobsService.startJob(id, userId);
    return {
      statusCode: HttpStatus.OK,
      message:
        "Start job successfully",
      data: true
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Post("/complete/:id")
  async completeJob(
    @Param("id") id: string,
    @Body('userId') userId: string,
    @Body('medias') medias: { mediaId: string[]; beforeAfter: string }[],
  ) {
    await this.jobsService.completeJob(id, userId, medias);
    return {
      statusCode: HttpStatus.OK,
      message:
        "Complete job successfully",
      data: true
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get("/cleaners/status/:jobId")
  async getCleanerStatusOfJob(
    @Param("jobId") id: string,
  ) {
    const resp = await this.jobsService.getCleanerStatusOfJob(id);
    return {
      statusCode: HttpStatus.OK,
      message:
        "Get cleaner status of job successfully",
      data: resp
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get("/cleaners/report/:jobId")
  async getCleanerReport(
    @Req() req: UserRequest,
    @Param("jobId") id: string,
  ) {
    let workspace = req.user.workspace || '';
    const resp = await this.jobsService.downloadCleanerReport(id, workspace.toString());
    return {
      statusCode: HttpStatus.OK,
      message:
        "Get cleaner report of job successfully",
      data: resp
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get(":id/occurrences")
  async getOccurrences(
    @Req() req: UserRequest,
    @Param('id') id: string,
    @Query('start') start: string,
    @Query('end') end: string,
  ) {
    if (!start || !end) {
      throw new HttpException(
        "Start and end date are required",
        HttpStatus.BAD_REQUEST
      );
    }
    return this.jobsService.getJobOccurrences(
      id,
      new Date(start),
      new Date(end)
    );
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @Delete(":id/occurrences/:date")
  async cancelOccurrences(
    @Param('id') id: string,
    @Param('date') date: string,
  ) {
    return this.jobsService.cancelOccurrence(
      id,
      new Date(date)
    );
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Patch(":id/update-occurrences")
  async updateOccurrences(
    @Param("id") id: string
  ) {
    return this.jobsService.triggerUpdateNextOccurrences(
      id
    );
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: "read",
    possession: "any"
  })
  @Get(":id/history")
  async getHistoricalJobs(
    @Req() req: UserRequest,
    @Param("id") id: string
  ) {
    const workspace = req.user.workspace || '';
    const jobs = await this.jobsService.getHistoricalJobs(
      id,
      workspace
    );
    return {
      statusCode: HttpStatus.OK,
      message: "Get historical jobs successfully",
      data: jobs
    };
  }

  @HttpCode(HttpStatus.OK)
  @UseGuards(AuthGuard, ACGuard)
  @UseRoles({
    resource: ROLE_RESOURCES.JOBS,
    action: 'update',
    possession: 'any'
  })
  @Patch(':id/invoice-scheduling')
  async updateInvoiceScheduling(
    @Param('id') id: string,
    @Body() updateDto: UpdateInvoiceSchedulingDto,
    @Req() req: UserRequest
  ) {
    const workspace = req.user.workspace || '';
    const updated = await this.invoicesService.updateScheduling(id, workspace, updateDto);

    return {
      statusCode: HttpStatus.OK,
      message: 'Update invoice scheduling successfully',
      data: updated
    };
  }

  /**
   * Send job notification webhook asynchronously
   * This method runs in the background and doesn't block job creation
   */
  private async sendJobNotificationAsync(jobId: string): Promise<void> {
    try {
      // Collect webhook data
      const webhookData = await this.jobsService.collectWebhookData(jobId);

      if (!webhookData) {
        console.warn(`Unable to collect webhook data for job: ${jobId}`);
        return;
      }

      // Send webhook notification
      await this.jobNotificationService.notifyJobPosted(
        webhookData.job,
        webhookData.bookingRequest,
        webhookData.address
      );
    } catch (error) {
      // Log error but don't throw - webhook failures shouldn't affect job creation
      console.error(`Failed to send webhook notification for job ${jobId}:`, error.message);
    }
  }
}
